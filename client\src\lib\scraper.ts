// Web scraping utilities (client-side interfaces)
export interface ScrapingResult {
  pricing?: {
    monthly?: string;
    yearly?: string;
    features?: string[];
  };
  company?: {
    name: string;
    logo?: string;
    description?: string;
  };
  features?: string[];
  network?: string;
  coverage?: string;
  performance?: {
    speed?: string;
    uptime?: string;
  };
}

export interface ScrapingJob {
  id: string;
  serviceId: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  result?: ScrapingResult;
  error?: string;
}

export class ScrapingService {
  static async startScraping(serviceId: number, url: string): Promise<ScrapingJob> {
    const response = await fetch('/api/scraping-jobs', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ serviceId, url })
    });
    
    if (!response.ok) {
      throw new Error('Failed to start scraping');
    }
    
    return response.json();
  }

  static async getScrapingStatus(jobId: string): Promise<ScrapingJob> {
    const response = await fetch(`/api/scraping-jobs/${jobId}`);
    
    if (!response.ok) {
      throw new Error('Failed to get scraping status');
    }
    
    return response.json();
  }

  static async pollScrapingJob(jobId: string, onUpdate: (job: ScrapingJob) => void): Promise<ScrapingJob> {
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          const job = await this.getScrapingStatus(jobId);
          onUpdate(job);
          
          if (job.status === 'completed' || job.status === 'failed') {
            resolve(job);
          } else {
            setTimeout(poll, 2000); // Poll every 2 seconds
          }
        } catch (error) {
          reject(error);
        }
      };
      
      poll();
    });
  }
}
