import { Check } from "lucide-react";

interface Service {
  id: number;
  name: string;
  scrapedData?: {
    pricing?: { monthly: string };
    features?: string[];
    network?: string;
  };
}

interface ComparisonTableProps {
  services: Service[];
  fields: string[];
}

const fieldLabels = {
  monthlyPrice: "Monthly Price",
  dataAllowance: "Data Allowance",
  mobileHotspot: "Mobile Hotspot",
  network: "Network",
  access5G: "5G Access"
};

export default function ComparisonTable({ services, fields }: ComparisonTableProps) {
  if (services.length === 0) return null;

  const getFieldValue = (service: Service, field: string) => {
    const data = service.scrapedData || {};
    
    switch (field) {
      case "monthlyPrice":
        return (
          <div className="text-center">
            <span className="text-lg font-bold text-green-600">
              {data.pricing?.monthly || '$25'}
            </span>
            <span className="text-xs text-gray-500 block">monthly</span>
          </div>
        );
      case "dataAllowance":
        return "Unlimited";
      case "mobileHotspot":
        return "Unlimited";
      case "network":
        return data.network || "Verizon";
      case "access5G":
        return <Check className="text-green-500 mx-auto" />;
      default:
        return "—";
    }
  };

  return (
    <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
      <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <h4 className="text-lg font-semibold text-gray-900">Detailed Comparison</h4>
      </div>
      
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Features
              </th>
              {services.map((service) => (
                <th key={service.id} className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {service.name}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {fields.map((field, index) => (
              <tr key={field} className={index % 2 === 1 ? "bg-gray-50" : ""}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {fieldLabels[field as keyof typeof fieldLabels] || field}
                </td>
                {services.map((service) => (
                  <td key={service.id} className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                    {getFieldValue(service, field)}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
