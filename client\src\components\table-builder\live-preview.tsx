import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";
import type { Service } from "@shared/schema";

interface LivePreviewProps {
  services: Service[];
  fields: string[];
  template: string;
}

export default function LivePreview({ services, fields, template }: LivePreviewProps) {
  const generateTable = () => {
    // In a real implementation, this would generate the actual HTML/CSS
    console.log("Generating table with:", { services, fields, template });
  };

  const exportHTML = () => {
    // In a real implementation, this would export the table as HTML
    console.log("Exporting HTML for:", { services, fields, template });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Live Preview</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-xs">
          <div className="overflow-x-auto">
            <table className="min-w-full border border-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-2 py-1 text-left border border-gray-200">Service</th>
                  {fields.includes("monthlyPrice") && (
                    <th className="px-2 py-1 text-left border border-gray-200">Price</th>
                  )}
                  {fields.includes("dataAllowance") && (
                    <th className="px-2 py-1 text-left border border-gray-200">Data</th>
                  )}
                  {fields.includes("networkCoverage") && (
                    <th className="px-2 py-1 text-left border border-gray-200">Network</th>
                  )}
                </tr>
              </thead>
              <tbody>
                {services.length === 0 ? (
                  <tr>
                    <td colSpan={fields.length + 1} className="px-2 py-4 text-center text-gray-500">
                      Select services to preview
                    </td>
                  </tr>
                ) : (
                  services.map((service, index) => (
                    <tr key={service.id} className={index % 2 === 1 ? "bg-gray-50" : ""}>
                      <td className="px-2 py-1 border border-gray-200">{service.name}</td>
                      {fields.includes("monthlyPrice") && (
                        <td className="px-2 py-1 border border-gray-200">$25/mo</td>
                      )}
                      {fields.includes("dataAllowance") && (
                        <td className="px-2 py-1 border border-gray-200">Unlimited</td>
                      )}
                      {fields.includes("networkCoverage") && (
                        <td className="px-2 py-1 border border-gray-200">Nationwide</td>
                      )}
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
        
        <div className="mt-4 space-y-2">
          <Button 
            className="w-full bg-blue-600 hover:bg-blue-700 text-sm"
            onClick={generateTable}
          >
            Generate Table
          </Button>
          <Button 
            variant="outline" 
            className="w-full text-sm"
            onClick={exportHTML}
          >
            <Download className="w-4 h-4 mr-2" />
            Export HTML
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
