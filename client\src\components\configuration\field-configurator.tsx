import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Trash2, Edit, Save, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import type { FieldTemplate, InsertFieldTemplate } from "@shared/schema";

const fieldTypes = [
  { value: "text", label: "Text" },
  { value: "number", label: "Number" },
  { value: "currency", label: "Currency" },
  { value: "boolean", label: "Boolean" },
  { value: "select", label: "Select" },
  { value: "multiselect", label: "Multi-select" },
  { value: "url", label: "URL" },
  { value: "image", label: "Image" },
];

interface FieldConfiguratorProps {
  nicheId?: number;
}

export default function FieldConfigurator({ nicheId }: FieldConfiguratorProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [editingField, setEditingField] = useState<number | null>(null);
  const [newField, setNewField] = useState<InsertFieldTemplate>({
    name: "",
    label: "",
    type: "text",
    validation: null,
    defaultValue: "",
    options: null,
    isRequired: false,
  });
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: fieldTemplates = [] } = useQuery<FieldTemplate[]>({
    queryKey: ["/api/field-templates"],
  });

  const createFieldMutation = useMutation({
    mutationFn: async (data: InsertFieldTemplate) => {
      const response = await fetch("/api/field-templates", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error("Failed to create field template");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/field-templates"] });
      setIsCreating(false);
      setNewField({
        name: "",
        label: "",
        type: "text",
        validation: null,
        defaultValue: "",
        options: null,
        isRequired: false,
      });
      toast({
        title: "Success",
        description: "Field template created successfully!",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create field template.",
        variant: "destructive",
      });
    },
  });

  const handleCreateField = () => {
    if (!newField.name || !newField.label) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    // Generate field name from label if not provided
    const fieldName = newField.name || newField.label.toLowerCase().replace(/[^a-z0-9]+/g, "");
    
    createFieldMutation.mutate({
      ...newField,
      name: fieldName,
    });
  };

  const handleFieldChange = (field: keyof InsertFieldTemplate, value: any) => {
    setNewField(prev => ({ ...prev, [field]: value }));
  };

  const renderFieldForm = (field: InsertFieldTemplate, isEditing: boolean = false) => (
    <div className="space-y-4 p-4 border rounded-lg">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="name">Field Name *</Label>
          <Input
            id="name"
            value={field.name}
            onChange={(e) => handleFieldChange("name", e.target.value)}
            placeholder="e.g., monthlyPrice, features"
          />
        </div>
        <div>
          <Label htmlFor="label">Display Label *</Label>
          <Input
            id="label"
            value={field.label}
            onChange={(e) => handleFieldChange("label", e.target.value)}
            placeholder="e.g., Monthly Price, Features"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="type">Field Type</Label>
          <Select value={field.type} onValueChange={(value) => handleFieldChange("type", value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {fieldTypes.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="defaultValue">Default Value</Label>
          <Input
            id="defaultValue"
            value={field.defaultValue || ""}
            onChange={(e) => handleFieldChange("defaultValue", e.target.value)}
            placeholder="Optional default value"
          />
        </div>
      </div>

      {(field.type === "select" || field.type === "multiselect") && (
        <div>
          <Label htmlFor="options">Options (comma-separated)</Label>
          <Textarea
            id="options"
            value={Array.isArray(field.options) ? field.options.join(", ") : ""}
            onChange={(e) => handleFieldChange("options", e.target.value.split(",").map(s => s.trim()))}
            placeholder="Option 1, Option 2, Option 3"
            rows={3}
          />
        </div>
      )}

      <div className="flex items-center space-x-2">
        <Checkbox
          id="isRequired"
          checked={field.isRequired}
          onCheckedChange={(checked) => handleFieldChange("isRequired", checked)}
        />
        <Label htmlFor="isRequired">Required field</Label>
      </div>

      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={() => setIsCreating(false)}>
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button onClick={handleCreateField} disabled={createFieldMutation.isPending}>
          <Save className="h-4 w-4 mr-2" />
          {createFieldMutation.isPending ? "Creating..." : "Create Field"}
        </Button>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Field Templates</CardTitle>
              <CardDescription>
                Manage field templates that can be used across different niches
              </CardDescription>
            </div>
            <Button onClick={() => setIsCreating(true)} disabled={isCreating}>
              <Plus className="h-4 w-4 mr-2" />
              Add Field Template
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {isCreating && renderFieldForm(newField)}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {fieldTemplates.map((template) => (
              <div
                key={template.id}
                className="border rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h3 className="font-medium">{template.label}</h3>
                    <p className="text-sm text-gray-500">{template.name}</p>
                  </div>
                  <div className="flex space-x-1">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setEditingField(template.id)}
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Badge variant="secondary" className="text-xs">
                    {template.type}
                  </Badge>
                  {template.isRequired && (
                    <Badge variant="outline" className="text-xs">
                      Required
                    </Badge>
                  )}
                  {template.defaultValue && (
                    <p className="text-xs text-gray-600">
                      Default: {template.defaultValue}
                    </p>
                  )}
                  {template.options && Array.isArray(template.options) && (
                    <p className="text-xs text-gray-600">
                      Options: {template.options.slice(0, 2).join(", ")}
                      {template.options.length > 2 && "..."}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>

          {fieldTemplates.length === 0 && !isCreating && (
            <div className="text-center py-8 text-gray-500">
              <p>No field templates created yet.</p>
              <Button 
                variant="outline" 
                className="mt-2"
                onClick={() => setIsCreating(true)}
              >
                Create your first field template
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
