import { useQuery } from "@tanstack/react-query";
import { useRoute } from "wouter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, ExternalLink, Star, Check, X } from "lucide-react";
import type { Service } from "@shared/schema";
import { Link } from "wouter";

export default function ServiceDetail() {
  const [match, params] = useRoute("/niche/:nicheSlug/service/:serviceId");
  const serviceId = params?.serviceId ? parseInt(params.serviceId) : null;
  const nicheSlug = params?.nicheSlug;

  const { data: service, isLoading } = useQuery<Service>({
    queryKey: ["/api/services", serviceId],
    enabled: !!serviceId,
  });

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Service Not Found</h1>
        <p className="text-gray-600">The requested service could not be found.</p>
        <Link href={`/niche/${nicheSlug}`}>
          <Button className="mt-4" variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Niche
          </Button>
        </Link>
      </div>
    );
  }

  const plans = [
    {
      name: "Basic Plan",
      price: service.scrapedData?.pricing?.monthly || "$9.99",
      features: service.scrapedData?.features || ["Feature 1", "Feature 2", "Feature 3"],
      popular: false,
    },
    {
      name: "Premium Plan", 
      price: "$" + (parseFloat(service.scrapedData?.pricing?.monthly?.replace('$', '') || "9.99") * 1.5).toFixed(2),
      features: [...(service.scrapedData?.features || []), "Advanced Feature", "Priority Support"],
      popular: true,
    },
    {
      name: "Enterprise Plan",
      price: "$" + (parseFloat(service.scrapedData?.pricing?.monthly?.replace('$', '') || "9.99") * 2.5).toFixed(2),
      features: [...(service.scrapedData?.features || []), "Advanced Feature", "Priority Support", "Custom Integration", "Dedicated Manager"],
      popular: false,
    },
  ];

  return (
    <div className="p-8">
      <div className="mb-8">
        <Link href={`/niche/${nicheSlug}`}>
          <Button variant="outline" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to {nicheSlug?.replace('-', ' ')}
          </Button>
        </Link>
        
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{service.name}</h1>
            <p className="text-gray-600 mt-2">Compare all plans and features for {service.name}</p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="secondary">{service.category}</Badge>
            <a 
              href={service.website} 
              target="_blank" 
              rel="noopener noreferrer"
              className="flex items-center gap-2 text-blue-600 hover:text-blue-800"
            >
              Visit Website
              <ExternalLink className="h-4 w-4" />
            </a>
          </div>
        </div>
      </div>

      {/* Service Overview */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Service Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Network Coverage</h3>
              <p className="text-gray-600">{service.scrapedData?.network || "Nationwide coverage available"}</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Starting Price</h3>
              <p className="text-2xl font-bold text-green-600">{service.scrapedData?.pricing?.monthly || "Contact for pricing"}</p>
              <p className="text-sm text-gray-500">per month</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Key Features</h3>
              <div className="flex flex-wrap gap-1">
                {(service.scrapedData?.features || []).slice(0, 3).map((feature, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {feature}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Plans Comparison */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Available Plans</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {plans.map((plan, index) => (
            <Card key={index} className={`relative ${plan.popular ? 'border-blue-500 shadow-lg' : ''}`}>
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-blue-500">
                    <Star className="h-3 w-3 mr-1" />
                    Most Popular
                  </Badge>
                </div>
              )}
              <CardHeader className="text-center">
                <CardTitle className="text-xl">{plan.name}</CardTitle>
                <div className="mt-4">
                  <span className="text-3xl font-bold text-gray-900">{plan.price}</span>
                  <span className="text-gray-500">/month</span>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center">
                      <Check className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button 
                  className={`w-full ${plan.popular ? 'bg-blue-500 hover:bg-blue-600' : ''}`}
                  variant={plan.popular ? "default" : "outline"}
                >
                  Choose {plan.name}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Feature Details */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Features</CardTitle>
          <CardDescription>Complete breakdown of what's included with {service.name}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Included Features</h3>
              <ul className="space-y-2">
                {(service.scrapedData?.features || []).map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-3" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">Service Information</h3>
              <div className="space-y-3">
                <div>
                  <span className="font-medium text-gray-900">Category:</span>
                  <span className="ml-2 text-gray-600">{service.category}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-900">Website:</span>
                  <a href={service.website} target="_blank" rel="noopener noreferrer" className="ml-2 text-blue-600 hover:text-blue-800">
                    {service.website}
                  </a>
                </div>
                <div>
                  <span className="font-medium text-gray-900">Last Updated:</span>
                  <span className="ml-2 text-gray-600">
                    {service.lastUpdated ? new Date(service.lastUpdated).toLocaleDateString() : "Recently"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}