import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { configurationService } from "./configuration";
import {
  insertServiceSchema,
  insertTableSchema,
  insertScrapingJobSchema,
  insertNicheSchema,
  insertFieldTemplateSchema,
  insertNicheConfigurationSchema,
  insertPlatformSettingSchema
} from "@shared/schema";
import { z } from "zod";

export async function registerRoutes(app: Express): Promise<Server> {
  // Configuration routes
  app.get("/api/field-templates", async (req, res) => {
    try {
      const templates = await configurationService.getFieldTemplates();
      res.json(templates);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch field templates" });
    }
  });

  app.post("/api/field-templates", async (req, res) => {
    try {
      const templateData = insertFieldTemplateSchema.parse(req.body);
      const template = await configurationService.createFieldTemplate(templateData);
      res.status(201).json(template);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid template data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create field template" });
    }
  });

  app.get("/api/niches/:id/configuration", async (req, res) => {
    try {
      const nicheId = parseInt(req.params.id);
      const configurations = await configurationService.getNicheConfigurations(nicheId);
      res.json(configurations);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch niche configuration" });
    }
  });

  app.post("/api/niches/:id/configuration", async (req, res) => {
    try {
      const nicheId = parseInt(req.params.id);
      const configData = insertNicheConfigurationSchema.parse({
        ...req.body,
        nicheId
      });
      const configuration = await configurationService.createNicheConfiguration(configData);
      res.status(201).json(configuration);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid configuration data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create niche configuration" });
    }
  });

  app.get("/api/platform-settings", async (req, res) => {
    try {
      const settings = await configurationService.getPlatformSettings();
      res.json(settings);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch platform settings" });
    }
  });

  app.put("/api/platform-settings/:key", async (req, res) => {
    try {
      const key = req.params.key;
      const { value } = req.body;
      const setting = await configurationService.updatePlatformSetting(key, value);
      if (!setting) {
        return res.status(404).json({ message: "Setting not found" });
      }
      res.json(setting);
    } catch (error) {
      res.status(500).json({ message: "Failed to update platform setting" });
    }
  });

  // Niche routes
  app.get("/api/niches", async (req, res) => {
    try {
      const niches = await storage.getNiches();
      res.json(niches);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch niches" });
    }
  });

  app.get("/api/niches/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const niche = await storage.getNiche(id);
      if (!niche) {
        return res.status(404).json({ message: "Niche not found" });
      }
      res.json(niche);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch niche" });
    }
  });

  app.get("/api/niches/:id/tables", async (req, res) => {
    try {
      const nicheId = parseInt(req.params.id);
      const tables = await storage.getTablesByNiche(nicheId);
      res.json(tables);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch tables for niche" });
    }
  });

  app.post("/api/niches", async (req, res) => {
    try {
      const nicheData = insertNicheSchema.parse(req.body);
      const niche = await storage.createNiche(nicheData);
      res.status(201).json(niche);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid niche data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create niche" });
    }
  });

  // Service routes
  app.get("/api/services", async (req, res) => {
    try {
      const services = await storage.getServices();
      res.json(services);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch services" });
    }
  });

  app.get("/api/services/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const service = await storage.getService(id);
      if (!service) {
        return res.status(404).json({ message: "Service not found" });
      }
      res.json(service);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch service" });
    }
  });

  app.post("/api/services", async (req, res) => {
    try {
      const validatedData = insertServiceSchema.parse(req.body);
      const service = await storage.createService(validatedData);
      
      // Create scraping job
      await storage.createScrapingJob({
        serviceId: service.id,
        status: "pending",
      });
      
      res.status(201).json(service);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid service data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create service" });
    }
  });

  app.put("/api/services/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updates = req.body;
      const service = await storage.updateService(id, updates);
      if (!service) {
        return res.status(404).json({ message: "Service not found" });
      }
      res.json(service);
    } catch (error) {
      res.status(500).json({ message: "Failed to update service" });
    }
  });

  app.delete("/api/services/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const deleted = await storage.deleteService(id);
      if (!deleted) {
        return res.status(404).json({ message: "Service not found" });
      }
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ message: "Failed to delete service" });
    }
  });

  // Table routes
  app.get("/api/tables", async (req, res) => {
    try {
      const tables = await storage.getTables();
      res.json(tables);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch tables" });
    }
  });

  app.get("/api/tables/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const table = await storage.getTable(id);
      if (!table) {
        return res.status(404).json({ message: "Table not found" });
      }
      res.json(table);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch table" });
    }
  });

  app.post("/api/tables", async (req, res) => {
    try {
      const validatedData = insertTableSchema.parse(req.body);
      const table = await storage.createTable(validatedData);
      res.status(201).json(table);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid table data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create table" });
    }
  });

  app.put("/api/tables/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updates = req.body;
      const table = await storage.updateTable(id, updates);
      if (!table) {
        return res.status(404).json({ message: "Table not found" });
      }
      res.json(table);
    } catch (error) {
      res.status(500).json({ message: "Failed to update table" });
    }
  });

  app.delete("/api/tables/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const deleted = await storage.deleteTable(id);
      if (!deleted) {
        return res.status(404).json({ message: "Table not found" });
      }
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ message: "Failed to delete table" });
    }
  });

  // Scraping job routes
  app.get("/api/scraping-jobs", async (req, res) => {
    try {
      const jobs = await storage.getScrapingJobs();
      res.json(jobs);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch scraping jobs" });
    }
  });

  app.post("/api/scraping-jobs/:id/start", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const job = await storage.updateScrapingJob(id, { 
        status: "running",
        progress: 0,
      });
      
      if (!job) {
        return res.status(404).json({ message: "Scraping job not found" });
      }

      // Simulate scraping process (in real implementation, this would trigger actual scraping)
      setTimeout(async () => {
        await storage.updateScrapingJob(id, {
          status: "completed",
          progress: 100,
          data: {
            pricing: { monthly: "$25", yearly: "$240" },
            features: ["Unlimited data", "5G access", "Mobile hotspot"],
            network: "Verizon",
            logo: "https://example.com/logo.png"
          }
        });
      }, 2000);

      res.json(job);
    } catch (error) {
      res.status(500).json({ message: "Failed to start scraping job" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
