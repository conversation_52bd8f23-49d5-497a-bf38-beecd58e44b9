import { pgTable, text, serial, integer, boolean, jsonb, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const services = pgTable("services", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  website: text("website").notNull(),
  category: text("category").notNull(),
  logo: text("logo"),
  status: text("status").notNull().default("active"), // active, updating, inactive
  scrapedData: jsonb("scraped_data"),
  lastUpdated: timestamp("last_updated").defaultNow(),
});

export const niches = pgTable("niches", {
  id: serial("id").primary<PERSON>ey(),
  name: text("name").notNull(),
  slug: text("slug").notNull().unique(),
  description: text("description"),
  icon: text("icon"),
  createdAt: timestamp("created_at").defaultNow(),
});

// New configuration tables for niche-agnostic platform
export const fieldTemplates = pgTable("field_templates", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  label: text("label").notNull(),
  type: text("type").notNull(), // text, number, currency, boolean, select, multiselect, url, image
  validation: jsonb("validation"), // validation rules
  defaultValue: text("default_value"),
  options: jsonb("options"), // for select/multiselect fields
  isRequired: boolean("is_required").default(false),
  createdAt: timestamp("created_at").defaultNow(),
});

export const nicheConfigurations = pgTable("niche_configurations", {
  id: serial("id").primaryKey(),
  nicheId: integer("niche_id").references(() => niches.id),
  fieldTemplateId: integer("field_template_id").references(() => fieldTemplates.id),
  displayOrder: integer("display_order").default(0),
  isVisible: boolean("is_visible").default(true),
  customLabel: text("custom_label"), // override default label
  customValidation: jsonb("custom_validation"), // override default validation
  createdAt: timestamp("created_at").defaultNow(),
});

export const platformSettings = pgTable("platform_settings", {
  id: serial("id").primaryKey(),
  key: text("key").notNull().unique(),
  value: jsonb("value").notNull(),
  description: text("description"),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const tables = pgTable("tables", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  nicheId: integer("niche_id").references(() => niches.id),
  template: text("template").notNull(),
  services: jsonb("services").$type<number[]>().notNull(),
  fields: jsonb("fields").$type<string[]>().notNull(),
  configuration: jsonb("configuration"),
  html: text("html"),
  isDefault: boolean("is_default").default(false),
  createdAt: timestamp("created_at").defaultNow(),
});

export const scrapingJobs = pgTable("scraping_jobs", {
  id: serial("id").primaryKey(),
  serviceId: integer("service_id").references(() => services.id),
  status: text("status").notNull(), // pending, running, completed, failed
  progress: integer("progress").default(0),
  data: jsonb("data"),
  error: text("error"),
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export const insertServiceSchema = createInsertSchema(services).pick({
  name: true,
  website: true,
  category: true,
});

export const insertNicheSchema = createInsertSchema(niches).pick({
  name: true,
  slug: true,
  description: true,
  icon: true,
});

export const insertTableSchema = createInsertSchema(tables).pick({
  name: true,
  nicheId: true,
  template: true,
  services: true,
  fields: true,
  configuration: true,
  isDefault: true,
});

export const insertScrapingJobSchema = createInsertSchema(scrapingJobs).pick({
  serviceId: true,
  status: true,
});

export const insertFieldTemplateSchema = createInsertSchema(fieldTemplates).pick({
  name: true,
  label: true,
  type: true,
  validation: true,
  defaultValue: true,
  options: true,
  isRequired: true,
});

export const insertNicheConfigurationSchema = createInsertSchema(nicheConfigurations).pick({
  nicheId: true,
  fieldTemplateId: true,
  displayOrder: true,
  isVisible: true,
  customLabel: true,
  customValidation: true,
});

export const insertPlatformSettingSchema = createInsertSchema(platformSettings).pick({
  key: true,
  value: true,
  description: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

export type InsertService = z.infer<typeof insertServiceSchema>;
export type Service = typeof services.$inferSelect;

export type InsertNiche = z.infer<typeof insertNicheSchema>;
export type Niche = typeof niches.$inferSelect;

export type InsertTable = z.infer<typeof insertTableSchema>;
export type Table = typeof tables.$inferSelect;

export type InsertScrapingJob = z.infer<typeof insertScrapingJobSchema>;
export type ScrapingJob = typeof scrapingJobs.$inferSelect;

export type InsertFieldTemplate = z.infer<typeof insertFieldTemplateSchema>;
export type FieldTemplate = typeof fieldTemplates.$inferSelect;

export type InsertNicheConfiguration = z.infer<typeof insertNicheConfigurationSchema>;
export type NicheConfiguration = typeof nicheConfigurations.$inferSelect;

export type InsertPlatformSetting = z.infer<typeof insertPlatformSettingSchema>;
export type PlatformSetting = typeof platformSettings.$inferSelect;
