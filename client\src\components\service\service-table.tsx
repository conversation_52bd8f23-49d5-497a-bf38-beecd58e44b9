import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { RefreshCw, Edit, Trash2 } from "lucide-react";
import type { Service } from "@shared/schema";

interface ServiceTableProps {
  services: Service[];
  isLoading: boolean;
  onDelete: (id: number) => void;
  onRefresh: (id: number) => void;
}

export default function ServiceTable({ services, isLoading, onDelete, onRefresh }: ServiceTableProps) {
  if (isLoading) {
    return <div className="text-center py-8">Loading services...</div>;
  }

  if (services.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No services added yet.</p>
        <p className="text-sm text-gray-400">Add your first service above to get started.</p>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "updating":
        return <Badge className="bg-yellow-100 text-yellow-800">Updating</Badge>;
      default:
        return <Badge variant="secondary">Inactive</Badge>;
    }
  };

  const getCategoryBadge = (category: string) => {
    return <Badge className="bg-blue-100 text-blue-800">{category}</Badge>;
  };

  return (
    <div className="overflow-x-auto">
      <table className="wp-table">
        <thead>
          <tr>
            <th className="text-left">Service</th>
            <th className="text-left">Category</th>
            <th className="text-left">Status</th>
            <th className="text-left">Last Updated</th>
            <th className="text-left">Actions</th>
          </tr>
        </thead>
        <tbody>
          {services.map((service) => (
            <tr key={service.id}>
              <td>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
                    <span className="text-xs font-bold">
                      {service.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <span className="font-medium">{service.name}</span>
                </div>
              </td>
              <td>{getCategoryBadge(service.category)}</td>
              <td>{getStatusBadge(service.status)}</td>
              <td>
                {service.lastUpdated 
                  ? new Date(service.lastUpdated).toLocaleDateString()
                  : "Never"
                }
              </td>
              <td>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-blue-600 hover:text-blue-700"
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-green-600 hover:text-green-700"
                    onClick={() => onRefresh(service.id)}
                  >
                    <RefreshCw className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                    onClick={() => onDelete(service.id)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
