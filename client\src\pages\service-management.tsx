import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import AddServiceForm from "@/components/service/add-service-form";
import ServiceTable from "@/components/service/service-table";
import { queryClient } from "@/lib/queryClient";
import type { Service } from "@shared/schema";

export default function ServiceManagement() {
  const { data: services = [], isLoading } = useQuery<Service[]>({
    queryKey: ["/api/services"],
  });

  const deleteServiceMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await fetch(`/api/services/${id}`, {
        method: "DELETE",
      });
      if (!response.ok) throw new Error("Failed to delete service");
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/services"] });
    },
  });

  const refreshServiceMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await fetch(`/api/scraping-jobs`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ serviceId: id, status: "pending" }),
      });
      if (!response.ok) throw new Error("Failed to start refresh");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/services"] });
    },
  });

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Service Management</CardTitle>
        </CardHeader>
        
        <CardContent>
          <AddServiceForm />
          
          <div className="mt-8">
            <ServiceTable 
              services={services}
              isLoading={isLoading}
              onDelete={(id) => deleteServiceMutation.mutate(id)}
              onRefresh={(id) => refreshServiceMutation.mutate(id)}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
