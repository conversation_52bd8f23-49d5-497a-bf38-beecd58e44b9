import { useQuery } from "@tanstack/react-query";
import { useRoute } from "wouter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Shield, Smartphone, Server, Mail, Users, Table, Plus, BarChart3 } from "lucide-react";
import type { Service, Table as TableType, Niche } from "@shared/schema";
import { Link } from "wouter";

const nicheIcons = {
  shield: Shield,
  smartphone: Smartphone,
  server: Server,
  mail: Mail,
};

export default function NicheDashboard() {
  const [match, params] = useRoute("/niche/:slug");
  const nicheSlug = params?.slug;

  const { data: niches = [] } = useQuery<Niche[]>({
    queryKey: ["/api/niches"],
  });

  const { data: services = [] } = useQuery<Service[]>({
    queryKey: ["/api/services"],
  });

  const { data: tables = [] } = useQuery<TableType[]>({
    queryKey: ["/api/tables"],
  });

  const currentNiche = niches.find(n => n.slug === nicheSlug);
  const nicheServices = services.filter(s => s.category === currentNiche?.name);
  const nicheTables = tables.filter(t => t.nicheId === currentNiche?.id);

  if (!currentNiche) {
    return (
      <div className="p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Niche Not Found</h1>
        <p className="text-gray-600">The requested niche could not be found.</p>
      </div>
    );
  }

  const IconComponent = nicheIcons[currentNiche.icon as keyof typeof nicheIcons] || Table;

  return (
    <div className="p-8">
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <div className="p-3 bg-blue-100 rounded-lg mr-4">
            <IconComponent className="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{currentNiche.name}</h1>
            <p className="text-gray-600 mt-1">{currentNiche.description}</p>
          </div>
        </div>
        
        <div className="flex gap-4">
          <Link href={`/niche/${nicheSlug}/compare`}>
            <Button className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Compare Services
            </Button>
          </Link>
          <Link href="/services">
            <Button variant="outline" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Service
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Services</CardTitle>
            <Users className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{nicheServices.length}</div>
            <p className="text-xs text-gray-500 mt-1">Active services in this niche</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Comparison Tables</CardTitle>
            <Table className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{nicheTables.length}</div>
            <p className="text-xs text-gray-500 mt-1">Ready-to-use tables</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Default Tables</CardTitle>
            <Shield className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {nicheTables.filter(t => t.isDefault).length}
            </div>
            <p className="text-xs text-gray-500 mt-1">Pre-built templates</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Services in {currentNiche.name}</CardTitle>
            <CardDescription>Click on any service to view detailed plans and pricing</CardDescription>
          </CardHeader>
          <CardContent>
            {nicheServices.length === 0 ? (
              <p className="text-gray-500 text-sm">No services added to this niche yet</p>
            ) : (
              <div className="space-y-3">
                {nicheServices.map((service) => (
                  <Link key={service.id} href={`/niche/${nicheSlug}/service/${service.id}`}>
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors">
                      <div>
                        <h3 className="font-medium text-gray-900">{service.name}</h3>
                        <p className="text-sm text-gray-500">
                          {service.scrapedData?.pricing?.monthly || "Pricing not available"}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">{service.category}</Badge>
                        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Comparison Tables</CardTitle>
            <CardDescription>Pre-built comparison tables for this niche</CardDescription>
          </CardHeader>
          <CardContent>
            {nicheTables.length === 0 ? (
              <p className="text-gray-500 text-sm">No tables available for this niche</p>
            ) : (
              <div className="space-y-3">
                {nicheTables.slice(0, 8).map((table) => (
                  <Link key={table.id} href={`/table-builder?table=${table.id}`}>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors">
                      <div>
                        <h3 className="font-medium text-gray-900 text-sm">{table.name}</h3>
                        <p className="text-xs text-gray-500 mt-1">
                          {table.services.length} services • {table.template} template
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        {table.isDefault && (
                          <Badge variant="outline" className="text-xs">Default</Badge>
                        )}
                        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}