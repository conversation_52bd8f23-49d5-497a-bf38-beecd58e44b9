import { users, services, tables, scrapingJobs, niches, type User, type InsertUser, type Service, type InsertService, type Table, type InsertTable, type ScrapingJob, type InsertScrapingJob, type Niche, type InsertNiche } from "@shared/schema";

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;

  // Niche methods
  getNiches(): Promise<Niche[]>;
  getNiche(id: number): Promise<Niche | undefined>;
  createNiche(niche: InsertNiche): Promise<Niche>;
  updateNiche(id: number, updates: Partial<Niche>): Promise<Niche | undefined>;
  deleteNiche(id: number): Promise<boolean>;

  // Service methods
  getServices(): Promise<Service[]>;
  getService(id: number): Promise<Service | undefined>;
  createService(service: InsertService): Promise<Service>;
  updateService(id: number, updates: Partial<Service>): Promise<Service | undefined>;
  deleteService(id: number): Promise<boolean>;

  // Table methods
  getTables(): Promise<Table[]>;
  getTable(id: number): Promise<Table | undefined>;
  getTablesByNiche(nicheId: number): Promise<Table[]>;
  createTable(table: InsertTable): Promise<Table>;
  updateTable(id: number, updates: Partial<Table>): Promise<Table | undefined>;
  deleteTable(id: number): Promise<boolean>;

  // Scraping job methods
  getScrapingJobs(): Promise<ScrapingJob[]>;
  getScrapingJob(id: number): Promise<ScrapingJob | undefined>;
  createScrapingJob(job: InsertScrapingJob): Promise<ScrapingJob>;
  updateScrapingJob(id: number, updates: Partial<ScrapingJob>): Promise<ScrapingJob | undefined>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private niches: Map<number, Niche>;
  private services: Map<number, Service>;
  private tables: Map<number, Table>;
  private scrapingJobs: Map<number, ScrapingJob>;
  private currentId: number;

  constructor() {
    this.users = new Map();
    this.niches = new Map();
    this.services = new Map();
    this.tables = new Map();
    this.scrapingJobs = new Map();
    this.currentId = 1;
    
    // Initialize with default niches and tables
    this.initializeDefaultData();
  }
  
  private async initializeDefaultData() {
    // Create sample niches for demonstration
    const sampleNiches = [
      {
        name: "Technology",
        slug: "technology",
        description: "Technology products and services comparison",
        icon: "server"
      },
      {
        name: "Health & Wellness",
        slug: "health-wellness",
        description: "Health and wellness products comparison",
        icon: "heart"
      },
      {
        name: "Finance",
        slug: "finance",
        description: "Financial services and products comparison",
        icon: "dollar-sign"
      }
    ];

    // Create sample niches but don't populate with hardcoded data
    for (const nicheData of sampleNiches) {
      const niche = await this.createNiche(nicheData);

      // Create a generic comparison table template for each niche
      await this.createTable({
        name: `${niche.name} Comparison Table`,
        nicheId: niche.id,
        template: "generic-comparison",
        services: [],
        fields: ["name", "price", "features", "rating", "website"],
        configuration: {
          layout: "table",
          showBadges: true,
          showLogos: true,
          showFeatures: true,
          showButtons: true,
          style: "modern"
        },
        isDefault: true
      });
    }
  }





  // User methods
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.username === username);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  // Niche methods
  async getNiches(): Promise<Niche[]> {
    return Array.from(this.niches.values());
  }

  async getNiche(id: number): Promise<Niche | undefined> {
    return this.niches.get(id);
  }

  async createNiche(insertNiche: InsertNiche): Promise<Niche> {
    const id = this.currentId++;
    const niche: Niche = {
      ...insertNiche,
      id,
      description: insertNiche.description || null,
      icon: insertNiche.icon || null,
      createdAt: new Date(),
    };
    this.niches.set(id, niche);
    return niche;
  }

  async updateNiche(id: number, updates: Partial<Niche>): Promise<Niche | undefined> {
    const niche = this.niches.get(id);
    if (!niche) return undefined;
    
    const updatedNiche = { ...niche, ...updates };
    this.niches.set(id, updatedNiche);
    return updatedNiche;
  }

  async deleteNiche(id: number): Promise<boolean> {
    return this.niches.delete(id);
  }

  // Service methods
  async getServices(): Promise<Service[]> {
    return Array.from(this.services.values());
  }

  async getService(id: number): Promise<Service | undefined> {
    return this.services.get(id);
  }

  async createService(insertService: InsertService): Promise<Service> {
    const id = this.currentId++;
    const service: Service = {
      ...insertService,
      id,
      status: "active",
      logo: null,
      scrapedData: null,
      lastUpdated: new Date(),
    };
    this.services.set(id, service);
    return service;
  }

  async updateService(id: number, updates: Partial<Service>): Promise<Service | undefined> {
    const service = this.services.get(id);
    if (!service) return undefined;
    
    const updatedService = { ...service, ...updates, lastUpdated: new Date() };
    this.services.set(id, updatedService);
    return updatedService;
  }

  async deleteService(id: number): Promise<boolean> {
    return this.services.delete(id);
  }

  // Table methods
  async getTables(): Promise<Table[]> {
    return Array.from(this.tables.values());
  }

  async getTable(id: number): Promise<Table | undefined> {
    return this.tables.get(id);
  }

  async getTablesByNiche(nicheId: number): Promise<Table[]> {
    return Array.from(this.tables.values()).filter(table => table.nicheId === nicheId);
  }

  async createTable(insertTable: InsertTable): Promise<Table> {
    const id = this.currentId++;
    const table: Table = {
      ...insertTable,
      id,
      html: null,
      createdAt: new Date(),
      services: insertTable.services || [],
      fields: insertTable.fields || [],
      configuration: insertTable.configuration || {},
      isDefault: insertTable.isDefault || false,
      nicheId: insertTable.nicheId || null,
    };
    this.tables.set(id, table);
    return table;
  }

  async updateTable(id: number, updates: Partial<Table>): Promise<Table | undefined> {
    const table = this.tables.get(id);
    if (!table) return undefined;
    
    const updatedTable = { ...table, ...updates };
    this.tables.set(id, updatedTable);
    return updatedTable;
  }

  async deleteTable(id: number): Promise<boolean> {
    return this.tables.delete(id);
  }

  // Scraping job methods
  async getScrapingJobs(): Promise<ScrapingJob[]> {
    return Array.from(this.scrapingJobs.values());
  }

  async getScrapingJob(id: number): Promise<ScrapingJob | undefined> {
    return this.scrapingJobs.get(id);
  }

  async createScrapingJob(insertJob: InsertScrapingJob): Promise<ScrapingJob> {
    const id = this.currentId++;
    const job: ScrapingJob = {
      ...insertJob,
      id,
      serviceId: insertJob.serviceId || null,
      progress: 0,
      data: null,
      error: null,
      createdAt: new Date(),
    };
    this.scrapingJobs.set(id, job);
    return job;
  }

  async updateScrapingJob(id: number, updates: Partial<ScrapingJob>): Promise<ScrapingJob | undefined> {
    const job = this.scrapingJobs.get(id);
    if (!job) return undefined;
    
    const updatedJob = { ...job, ...updates };
    this.scrapingJobs.set(id, updatedJob);
    return updatedJob;
  }
}

export const storage = new MemStorage();
