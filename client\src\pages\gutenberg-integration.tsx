import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Box, Settings, Save, Download, Code } from "lucide-react";
import ComparisonCards from "@/components/comparison/comparison-cards";
import ComparisonTable from "@/components/comparison/comparison-table";
import type { Service, Niche } from "@shared/schema";

export default function GutenbergIntegration() {
  const [selectedNiche, setSelectedNiche] = useState<string>("");
  const [selectedTemplate, setSelectedTemplate] = useState<string>("cards-table");

  const { data: niches = [] } = useQuery<Niche[]>({
    queryKey: ["/api/niches"],
  });

  const { data: services = [] } = useQuery<Service[]>({
    queryKey: ["/api/services"],
  });

  // Get services for selected niche
  const nicheServices = selectedNiche
    ? services.filter(s => s.category === selectedNiche).slice(0, 3)
    : [];

  // Create mock data structure for display
  const mockServices = nicheServices.map((service, index) => ({
    ...service,
    scrapedData: {
      pricing: { monthly: `$${(15 + index * 5)}`, yearly: `$${(180 + index * 60)}` },
      features: [
        "Feature 1 for " + service.name,
        "Feature 2 for " + service.name,
        "Feature 3 for " + service.name
      ],
      badge: index === 0 ? "BEST VALUE" : index === 1 ? "MOST POPULAR" : null
    }
  }));

  const fields = ["monthlyPrice", "features", "rating", "website"];

  const generateBlockCode = () => {
    if (!selectedNiche || nicheServices.length === 0) {
      return "<!-- Select a niche and ensure it has services to generate block code -->";
    }

    return `<!-- wp:affiliate-comparison-block {"niche":"${selectedNiche}","template":"${selectedTemplate}","services":[${nicheServices.map(s => s.id).join(',')}]} -->
<div class="wp-block-affiliate-comparison-block">
  <!-- This block will render your comparison table -->
  <!-- Services: ${nicheServices.map(s => s.name).join(', ')} -->
  <!-- Template: ${selectedTemplate} -->
</div>
<!-- /wp:affiliate-comparison-block -->`;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Box className="w-5 h-5 mr-2" />
            WordPress Gutenberg Block Integration
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Configuration Panel */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h5 className="font-medium text-gray-900 mb-3">Block Configuration</h5>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Select Niche</label>
                <Select value={selectedNiche} onValueChange={setSelectedNiche}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a niche" />
                  </SelectTrigger>
                  <SelectContent>
                    {niches.map((niche) => (
                      <SelectItem key={niche.id} value={niche.name}>
                        {niche.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Template Style</label>
                <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cards-table">Cards + Table</SelectItem>
                    <SelectItem value="table-only">Table Only</SelectItem>
                    <SelectItem value="cards-only">Cards Only</SelectItem>
                    <SelectItem value="grid">Grid Layout</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Block Editor Simulation */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8">
            <div className="text-center mb-6">
              <Box className="text-4xl text-gray-400 mb-3 mx-auto w-16 h-16" />
              <h4 className="text-lg font-medium text-gray-900">Universal Comparison Table Block</h4>
              <p className="text-sm text-gray-500">
                {selectedNiche
                  ? `Showing ${selectedNiche} comparison table`
                  : "Select a niche to preview the block"}
              </p>
            </div>

            {selectedNiche && nicheServices.length > 0 ? (
              <div className="space-y-6">
                {/* Hero Cards Layout */}
                {(selectedTemplate === "cards-table" || selectedTemplate === "cards-only") && (
                  <ComparisonCards services={mockServices.slice(0, 2)} />
                )}

                {/* Detailed Comparison Table */}
                {(selectedTemplate === "cards-table" || selectedTemplate === "table-only") && (
                  <ComparisonTable services={mockServices} fields={fields} />
                )}

                {/* Grid Layout */}
                {selectedTemplate === "grid" && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {mockServices.map((service) => (
                      <div key={service.id} className="border rounded-lg p-4">
                        <h3 className="font-semibold">{service.name}</h3>
                        <p className="text-sm text-gray-600">{service.scrapedData?.pricing?.monthly}/month</p>
                        <Button className="w-full mt-2" size="sm">Learn More</Button>
                      </div>
                    ))}
                  </div>
                )}

                {/* CTA Section */}
                <div className="text-center bg-gray-50 rounded-lg p-6">
                  <h5 className="text-lg font-semibold text-gray-900 mb-2">Ready to Choose?</h5>
                  <p className="text-gray-600 mb-4">Compare all options and find the perfect solution for your needs.</p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    {mockServices.map((service, index) => (
                      <Button
                        key={service.id}
                        className={`${
                          index === 0 ? "bg-green-600 hover:bg-green-700" :
                          index === 1 ? "bg-blue-600 hover:bg-blue-700" :
                          "bg-purple-600 hover:bg-purple-700"
                        }`}
                      >
                        Get {service.name}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                {!selectedNiche
                  ? "Select a niche to preview the comparison block"
                  : "No services found for this niche. Add some services first."}
              </div>
            )}
          </div>

          {/* Block Code Generation */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h5 className="font-medium text-gray-900 mb-3">WordPress Block Code</h5>
            <div className="bg-gray-800 text-green-400 p-4 rounded-md font-mono text-sm overflow-x-auto">
              <pre>{generateBlockCode()}</pre>
            </div>

            <div className="mt-4 flex justify-between">
              <Button variant="outline">
                <Settings className="w-4 h-4 mr-2" />
                Advanced Settings
              </Button>
              <div className="space-x-2">
                <Button
                  variant="outline"
                  onClick={() => navigator.clipboard.writeText(generateBlockCode())}
                >
                  <Code className="w-4 h-4 mr-2" />
                  Copy Block Code
                </Button>
                <Button>
                  <Save className="w-4 h-4 mr-2" />
                  Save Configuration
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
