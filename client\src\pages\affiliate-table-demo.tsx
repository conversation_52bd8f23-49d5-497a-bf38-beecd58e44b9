import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TableGenerator } from "@/lib/table-generator";
import type { Service, Niche } from "@shared/schema";

export default function AffiliateTableDemo() {
  const [selectedNiche, setSelectedNiche] = useState<string>("");

  const { data: services = [] } = useQuery<Service[]>({
    queryKey: ["/api/services"],
  });

  const { data: niches = [] } = useQuery<Niche[]>({
    queryKey: ["/api/niches"],
  });

  // Filter services for selected niche
  const nicheServices = selectedNiche
    ? services.filter(service => service.category === selectedNiche)
    : [];

  // Generate affiliate table for selected niche
  const generateAffiliateTable = () => {
    if (nicheServices.length === 0) return { html: '', css: '' };

    return TableGenerator.generateHTML(nicheServices, {
      template: "affiliate-cards",
      services: nicheServices.map(s => s.id),
      fields: ["name", "price", "features"],
      style: {
        theme: "modern",
        showBadges: true,
        showCTA: true,
        responsiveBreakpoint: "768px"
      }
    });
  };

  const tableCode = generateAffiliateTable();

  const copyToClipboard = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Professional Affiliate Tables</h1>
          <p className="text-gray-600 mt-2">Generate professional comparison tables for any niche</p>
        </div>
      </div>

      {/* Niche Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Niche</CardTitle>
        </CardHeader>
        <CardContent>
          <Select value={selectedNiche} onValueChange={setSelectedNiche}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Choose a niche to generate tables for" />
            </SelectTrigger>
            <SelectContent>
              {niches.map((niche) => (
                <SelectItem key={niche.id} value={niche.name}>
                  {niche.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Live Preview */}
      <Card>
        <CardHeader>
          <CardTitle>
            Live Preview - {selectedNiche ? `${selectedNiche} Comparison` : "Select a Niche"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {selectedNiche && nicheServices.length > 0 ? (
            <div className="border rounded-lg p-4 bg-gray-50">
              <style dangerouslySetInnerHTML={{ __html: tableCode.css }} />
              <div dangerouslySetInnerHTML={{ __html: tableCode.html }} />
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              {!selectedNiche
                ? "Select a niche to preview the affiliate table"
                : "No services found for this niche. Add some services first."}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Code Export */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-lg">HTML Code</CardTitle>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => copyToClipboard(tableCode.html)}
            >
              Copy HTML
            </Button>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-gray-100 p-4 rounded overflow-x-auto max-h-96">
              <code>{tableCode.html}</code>
            </pre>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-lg">CSS Code</CardTitle>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => copyToClipboard(tableCode.css)}
            >
              Copy CSS
            </Button>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-gray-100 p-4 rounded overflow-x-auto max-h-96">
              <code>{tableCode.css}</code>
            </pre>
          </CardContent>
        </Card>
      </div>

      {/* Features */}
      <Card>
        <CardHeader>
          <CardTitle>Affiliate Table Features</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-green-700">Professional Design</h3>
              <p className="text-sm text-gray-600 mt-1">Clean, modern cards with proper spacing and typography</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-blue-700">Prominent Pricing</h3>
              <p className="text-sm text-gray-600 mt-1">Large, clear pricing display with tax inclusion notes</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-purple-700">Call-to-Action Buttons</h3>
              <p className="text-sm text-gray-600 mt-1">"Get Started" buttons for conversion optimization</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-orange-700">Popular Badges</h3>
              <p className="text-sm text-gray-600 mt-1">"Most Popular" and "Best Value" promotional badges</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-red-700">Feature Lists</h3>
              <p className="text-sm text-gray-600 mt-1">Clear feature breakdown for easy comparison</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-indigo-700">Mobile Responsive</h3>
              <p className="text-sm text-gray-600 mt-1">Optimized for all screen sizes and devices</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}