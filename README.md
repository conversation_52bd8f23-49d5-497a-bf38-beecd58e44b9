# Universal Affiliate Platform

A flexible, niche-agnostic affiliate marketing platform that can be quickly configured for any industry while maintaining WordPress Gutenberg block integration as its core differentiator.

## 🚀 Features

### ✨ Niche-Agnostic Design
- **Universal Configuration**: Create comparison tables for any industry (VPN, hosting, health, finance, technology, etc.)
- **Dynamic Field System**: Define custom fields for different types of services
- **Flexible Templates**: Multiple table layouts and styling options
- **Easy Niche Setup**: Wizard-guided niche creation process

### 🔧 WordPress Integration
- **Gutenberg Block Support**: Generate professional WordPress blocks
- **SEO-Friendly Output**: Clean, semantic HTML for better search rankings
- **Responsive Design**: Tables work perfectly on all devices
- **Copy-Paste Ready**: Export HTML/CSS for any CMS

### 📊 Service Management
- **Multi-Niche Support**: Manage services across different industries
- **Custom Data Fields**: Track any type of service information
- **Automated Scraping**: Built-in web scraping capabilities (extensible)
- **Status Tracking**: Monitor service updates and availability

### 🎨 Professional Templates
- **Card Layouts**: Modern card-based comparison displays
- **Table Views**: Traditional comparison tables
- **Grid Layouts**: Flexible grid-based presentations
- **Custom Styling**: Configurable themes and colors

## 🛠 Technology Stack

- **Frontend**: React 18 + TypeScript + Vite
- **Backend**: Express.js + TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **UI Components**: Radix UI + Tailwind CSS
- **State Management**: TanStack Query
- **Routing**: Wouter (lightweight React router)

## 📁 Project Structure

```
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Application pages
│   │   └── lib/            # Utilities and helpers
├── server/                 # Express backend
│   ├── index.ts           # Server entry point
│   ├── routes.ts          # API routes
│   ├── storage.ts         # Data storage layer
│   └── configuration.ts   # Configuration service
├── shared/                 # Shared types and schemas
│   └── schema.ts          # Database schema and types
└── README.md
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- PostgreSQL database
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd affiliate-insight-blocks
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   # Create .env file
   DATABASE_URL=postgresql://username:password@localhost:5432/database_name
   ```

4. **Run database migrations**
   ```bash
   npm run db:push
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to `http://localhost:5000`

## 📖 Usage Guide

### 1. Create Your First Niche

1. Click "Create Niche" in the sidebar
2. Fill in basic information (name, description, icon)
3. Select which fields you want to track
4. Save your niche configuration

### 2. Add Services

1. Go to "Add Services"
2. Select your niche
3. Enter service details
4. The system will create a scraping job for data collection

### 3. Build Comparison Tables

1. Use the "Table Builder" to create custom tables
2. Select services to include
3. Choose fields to display
4. Customize the layout and styling

### 4. WordPress Integration

1. Go to "Gutenberg Integration"
2. Select your niche and template
3. Preview the block
4. Copy the generated block code
5. Paste into your WordPress editor

## 🔧 Configuration

### Platform Settings

Access platform-wide settings via the "Platform Settings" page:

- **Platform Name**: Customize your platform branding
- **Default Currency**: Set the default currency for pricing fields
- **Gutenberg Integration**: Enable/disable WordPress features

### Field Templates

Create reusable field templates that can be applied across niches:

- **Text Fields**: Names, descriptions, categories
- **Currency Fields**: Pricing, costs, fees
- **Select Fields**: Predefined options (ratings, categories)
- **Boolean Fields**: Yes/no features
- **URL Fields**: Websites, affiliate links
- **Image Fields**: Logos, screenshots

### Niche Configuration

For each niche, configure:

- **Active Fields**: Which fields to display
- **Field Order**: How fields are arranged
- **Custom Labels**: Override default field names
- **Validation Rules**: Data requirements

## 🎯 Use Cases

### VPN Services
- Track pricing, server locations, protocols
- Compare speeds, logging policies, features
- Generate affiliate comparison tables

### Web Hosting
- Monitor pricing, storage, bandwidth
- Compare uptime, support, features
- Create hosting comparison guides

### Health & Wellness
- Track supplement ingredients, dosages
- Compare prices, certifications, reviews
- Build product comparison tables

### Financial Services
- Monitor rates, fees, requirements
- Compare features, benefits, terms
- Generate financial product comparisons

## 🔌 API Endpoints

### Niches
- `GET /api/niches` - List all niches
- `POST /api/niches` - Create new niche
- `GET /api/niches/:id` - Get niche details

### Services
- `GET /api/services` - List all services
- `POST /api/services` - Add new service
- `PUT /api/services/:id` - Update service

### Field Templates
- `GET /api/field-templates` - List field templates
- `POST /api/field-templates` - Create field template

### Configuration
- `GET /api/niches/:id/configuration` - Get niche field config
- `POST /api/niches/:id/configuration` - Set field configuration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the code examples

---

**Built with ❤️ for affiliate marketers who need flexibility and professional results.**
