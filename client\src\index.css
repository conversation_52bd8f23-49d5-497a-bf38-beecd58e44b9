@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(35, 9%, 14%);
  --muted: hsl(35, 4%, 95%);
  --muted-foreground: hsl(35, 5%, 45%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(35, 9%, 14%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(35, 9%, 14%);
  --border: hsl(35, 5%, 90%);
  --input: hsl(35, 5%, 90%);
  --primary: hsl(200, 100%, 34%);
  --primary-foreground: hsl(209, 100%, 99%);
  --secondary: hsl(35, 4%, 95%);
  --secondary-foreground: hsl(24, 10%, 10%);
  --accent: hsl(35, 4%, 95%);
  --accent-foreground: hsl(24, 10%, 10%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(60, 9%, 98%);
  --ring: hsl(35, 9%, 14%);
  --radius: 0.5rem;
  
  /* WordPress specific colors */
  --wp-blue: hsl(200, 100%, 34%);
  --wp-light-blue: hsl(193, 100%, 41%);
  --wp-grey: hsl(0, 0%, 95%);
  --wp-dark: hsl(213, 11%, 16%);
  --wp-red: hsl(354, 62%, 52%);
  --wp-green: hsl(142, 100%, 32%);
}

.dark {
  --background: hsl(240, 10%, 4%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 4%, 16%);
  --muted-foreground: hsl(240, 5%, 65%);
  --popover: hsl(240, 10%, 4%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 4%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 4%, 16%);
  --input: hsl(240, 4%, 16%);
  --primary: hsl(200, 100%, 34%);
  --primary-foreground: hsl(209, 100%, 99%);
  --secondary: hsl(240, 4%, 16%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 4%, 16%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 63%, 31%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 5%, 84%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

@layer components {
  .wp-admin-bar {
    @apply hidden;
  }
  
  .notice {
    @apply border-l-4 px-3 py-1;
  }
  
  .notice-success {
    border-left-color: hsl(142, 100%, 32%);
    background-color: hsl(216, 100%, 97%);
  }
  
  .wp-table {
    @apply border-collapse w-full;
  }
  
  .wp-table th,
  .wp-table td {
    @apply border border-gray-300 px-2 py-2;
  }
  
  .wp-table th {
    @apply bg-gray-50 font-semibold;
  }
  
  .wp-table tbody tr:nth-child(even) {
    @apply bg-gray-50;
  }
  
  .comparison-card {
    @apply transition-all duration-300 ease-in-out;
  }
  
  .comparison-card:hover {
    @apply -translate-y-0.5 shadow-lg;
  }
  
  .drag-handle {
    @apply cursor-move;
  }
  
  .sortable-ghost {
    @apply opacity-40;
  }
}
