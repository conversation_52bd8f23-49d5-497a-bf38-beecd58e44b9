import type { 
  FieldTemplate, 
  InsertFieldTemplate, 
  NicheConfiguration, 
  InsertNicheConfiguration,
  PlatformSetting,
  InsertPlatformSetting 
} from "@shared/schema";

export interface IConfigurationService {
  // Field Template methods
  getFieldTemplates(): Promise<FieldTemplate[]>;
  getFieldTemplate(id: number): Promise<FieldTemplate | undefined>;
  createFieldTemplate(template: InsertFieldTemplate): Promise<FieldTemplate>;
  updateFieldTemplate(id: number, updates: Partial<FieldTemplate>): Promise<FieldTemplate | undefined>;
  deleteFieldTemplate(id: number): Promise<boolean>;

  // Niche Configuration methods
  getNicheConfigurations(nicheId: number): Promise<NicheConfiguration[]>;
  createNicheConfiguration(config: InsertNicheConfiguration): Promise<NicheConfiguration>;
  updateNicheConfiguration(id: number, updates: Partial<NicheConfiguration>): Promise<NicheConfiguration | undefined>;
  deleteNicheConfiguration(id: number): Promise<boolean>;

  // Platform Settings methods
  getPlatformSettings(): Promise<PlatformSetting[]>;
  getPlatformSetting(key: string): Promise<PlatformSetting | undefined>;
  setPlatformSetting(setting: InsertPlatformSetting): Promise<PlatformSetting>;
  updatePlatformSetting(key: string, value: any): Promise<PlatformSetting | undefined>;

  // Helper methods
  getConfiguredFieldsForNiche(nicheId: number): Promise<FieldTemplate[]>;
  initializeDefaultFieldTemplates(): Promise<void>;
}

export class ConfigurationService implements IConfigurationService {
  private fieldTemplates: Map<number, FieldTemplate>;
  private nicheConfigurations: Map<number, NicheConfiguration>;
  private platformSettings: Map<string, PlatformSetting>;
  private currentId: number;

  constructor() {
    this.fieldTemplates = new Map();
    this.nicheConfigurations = new Map();
    this.platformSettings = new Map();
    this.currentId = 1000; // Start with higher ID to avoid conflicts
    
    this.initializeDefaultFieldTemplates();
  }

  async initializeDefaultFieldTemplates(): Promise<void> {
    // Common field templates that work across all niches
    const defaultTemplates: InsertFieldTemplate[] = [
      {
        name: "name",
        label: "Service Name",
        type: "text",
        validation: { required: true, minLength: 2, maxLength: 100 },
        isRequired: true
      },
      {
        name: "price",
        label: "Price",
        type: "currency",
        validation: { required: true, min: 0 },
        isRequired: true
      },
      {
        name: "monthlyPrice",
        label: "Monthly Price",
        type: "currency",
        validation: { required: true, min: 0 },
        isRequired: true
      },
      {
        name: "yearlyPrice",
        label: "Yearly Price",
        type: "currency",
        validation: { min: 0 },
        isRequired: false
      },
      {
        name: "features",
        label: "Features",
        type: "multiselect",
        validation: { required: false },
        isRequired: false
      },
      {
        name: "rating",
        label: "Rating",
        type: "number",
        validation: { min: 0, max: 5, step: 0.1 },
        isRequired: false
      },
      {
        name: "website",
        label: "Website",
        type: "url",
        validation: { required: true },
        isRequired: true
      },
      {
        name: "logo",
        label: "Logo",
        type: "image",
        validation: { required: false },
        isRequired: false
      },
      {
        name: "description",
        label: "Description",
        type: "text",
        validation: { maxLength: 500 },
        isRequired: false
      },
      {
        name: "badge",
        label: "Badge",
        type: "select",
        options: ["Best Value", "Most Popular", "Editor's Choice", "Best Overall", "Premium"],
        validation: { required: false },
        isRequired: false
      },
      {
        name: "category",
        label: "Category",
        type: "text",
        validation: { required: true, maxLength: 50 },
        isRequired: true
      },
      {
        name: "availability",
        label: "Availability",
        type: "select",
        options: ["Available", "Limited", "Coming Soon", "Discontinued"],
        validation: { required: false },
        defaultValue: "Available",
        isRequired: false
      }
    ];

    for (const template of defaultTemplates) {
      await this.createFieldTemplate(template);
    }

    // Set default platform settings
    await this.setPlatformSetting({
      key: "platform_name",
      value: "Universal Affiliate Platform",
      description: "The name of the platform"
    });

    await this.setPlatformSetting({
      key: "default_currency",
      value: "USD",
      description: "Default currency for pricing fields"
    });

    await this.setPlatformSetting({
      key: "gutenberg_enabled",
      value: true,
      description: "Enable WordPress Gutenberg block integration"
    });
  }

  // Field Template methods
  async getFieldTemplates(): Promise<FieldTemplate[]> {
    return Array.from(this.fieldTemplates.values());
  }

  async getFieldTemplate(id: number): Promise<FieldTemplate | undefined> {
    return this.fieldTemplates.get(id);
  }

  async createFieldTemplate(insertTemplate: InsertFieldTemplate): Promise<FieldTemplate> {
    const id = this.currentId++;
    const template: FieldTemplate = {
      ...insertTemplate,
      id,
      validation: insertTemplate.validation || null,
      defaultValue: insertTemplate.defaultValue || null,
      options: insertTemplate.options || null,
      isRequired: insertTemplate.isRequired || false,
      createdAt: new Date(),
    };
    this.fieldTemplates.set(id, template);
    return template;
  }

  async updateFieldTemplate(id: number, updates: Partial<FieldTemplate>): Promise<FieldTemplate | undefined> {
    const template = this.fieldTemplates.get(id);
    if (!template) return undefined;
    
    const updatedTemplate = { ...template, ...updates };
    this.fieldTemplates.set(id, updatedTemplate);
    return updatedTemplate;
  }

  async deleteFieldTemplate(id: number): Promise<boolean> {
    return this.fieldTemplates.delete(id);
  }

  // Niche Configuration methods
  async getNicheConfigurations(nicheId: number): Promise<NicheConfiguration[]> {
    return Array.from(this.nicheConfigurations.values())
      .filter(config => config.nicheId === nicheId)
      .sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0));
  }

  async createNicheConfiguration(insertConfig: InsertNicheConfiguration): Promise<NicheConfiguration> {
    const id = this.currentId++;
    const config: NicheConfiguration = {
      ...insertConfig,
      id,
      displayOrder: insertConfig.displayOrder || 0,
      isVisible: insertConfig.isVisible !== false,
      customLabel: insertConfig.customLabel || null,
      customValidation: insertConfig.customValidation || null,
      createdAt: new Date(),
    };
    this.nicheConfigurations.set(id, config);
    return config;
  }

  async updateNicheConfiguration(id: number, updates: Partial<NicheConfiguration>): Promise<NicheConfiguration | undefined> {
    const config = this.nicheConfigurations.get(id);
    if (!config) return undefined;
    
    const updatedConfig = { ...config, ...updates };
    this.nicheConfigurations.set(id, updatedConfig);
    return updatedConfig;
  }

  async deleteNicheConfiguration(id: number): Promise<boolean> {
    return this.nicheConfigurations.delete(id);
  }

  // Platform Settings methods
  async getPlatformSettings(): Promise<PlatformSetting[]> {
    return Array.from(this.platformSettings.values());
  }

  async getPlatformSetting(key: string): Promise<PlatformSetting | undefined> {
    return this.platformSettings.get(key);
  }

  async setPlatformSetting(insertSetting: InsertPlatformSetting): Promise<PlatformSetting> {
    const id = this.currentId++;
    const setting: PlatformSetting = {
      ...insertSetting,
      id,
      description: insertSetting.description || null,
      updatedAt: new Date(),
    };
    this.platformSettings.set(insertSetting.key, setting);
    return setting;
  }

  async updatePlatformSetting(key: string, value: any): Promise<PlatformSetting | undefined> {
    const setting = this.platformSettings.get(key);
    if (!setting) return undefined;
    
    const updatedSetting = { ...setting, value, updatedAt: new Date() };
    this.platformSettings.set(key, updatedSetting);
    return updatedSetting;
  }

  // Helper methods
  async getConfiguredFieldsForNiche(nicheId: number): Promise<FieldTemplate[]> {
    const configurations = await this.getNicheConfigurations(nicheId);
    const fields: FieldTemplate[] = [];
    
    for (const config of configurations) {
      if (config.isVisible) {
        const template = await this.getFieldTemplate(config.fieldTemplateId);
        if (template) {
          // Apply custom overrides
          const customizedTemplate = {
            ...template,
            label: config.customLabel || template.label,
            validation: config.customValidation || template.validation,
          };
          fields.push(customizedTemplate);
        }
      }
    }
    
    return fields;
  }
}

export const configurationService = new ConfigurationService();
