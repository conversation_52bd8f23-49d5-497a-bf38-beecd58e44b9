import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { queryClient } from "@/lib/queryClient";
import { Bot } from "lucide-react";

export default function AddServiceForm() {
  const [formData, setFormData] = useState({
    name: "",
    website: "",
    category: ""
  });

  const { toast } = useToast();

  const createServiceMutation = useMutation({
    mutationFn: async (data: typeof formData) => {
      const response = await fetch("/api/services", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data)
      });
      if (!response.ok) throw new Error("Failed to create service");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/services"] });
      setFormData({ name: "", website: "", category: "" });
      toast({
        title: "Service Added",
        description: "Auto-discovery process has been started",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to add service",
        variant: "destructive",
      });
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name || !formData.website) return;
    
    const category = formData.category || "auto-detect";
    createServiceMutation.mutate({ ...formData, category });
  };

  return (
    <div className="mb-8 p-4 bg-gray-50 rounded-lg">
      <h4 className="font-medium text-gray-900 mb-4">Quick Add Service</h4>
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="service-name">Service Name</Label>
            <Input
              id="service-name"
              placeholder="e.g., NordVPN"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            />
          </div>
          <div>
            <Label htmlFor="website-url">Website URL</Label>
            <Input
              id="website-url"
              type="url"
              placeholder="https://nordvpn.com"
              value={formData.website}
              onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
            />
          </div>
          <div>
            <Label htmlFor="niche-category">Niche Category</Label>
            <Select value={formData.category} onValueChange={(value) => 
              setFormData(prev => ({ ...prev, category: value }))
            }>
              <SelectTrigger>
                <SelectValue placeholder="Auto-detect" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="auto-detect">Auto-detect</SelectItem>
                <SelectItem value="vpn-services">VPN Services</SelectItem>
                <SelectItem value="phone-plans">Phone Plans</SelectItem>
                <SelectItem value="web-hosting">Web Hosting</SelectItem>
                <SelectItem value="email-marketing">Email Marketing</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <Button 
          type="submit" 
          className="mt-4 bg-green-600 hover:bg-green-700"
          disabled={createServiceMutation.isPending}
        >
          <Bot className="w-4 h-4 mr-2" />
          {createServiceMutation.isPending ? "Processing..." : "Auto-Discover & Add"}
        </Button>
      </form>
    </div>
  );
}
