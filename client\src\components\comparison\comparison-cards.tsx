import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";

interface Service {
  id: number;
  name: string;
  scrapedData?: {
    pricing?: { monthly: string };
    features?: string[];
    network?: string;
    badge?: string;
  };
}

interface ComparisonCardsProps {
  services: Service[];
}

export default function ComparisonCards({ services }: ComparisonCardsProps) {
  if (services.length === 0) return null;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {services.slice(0, 2).map((service, index) => {
        const isFirst = index === 0;
        const data = service.scrapedData || {};
        const brandColors = [
          { bg: "from-blue-600 to-blue-700", accent: "bg-green-500" },
          { bg: "from-red-500 to-red-600", accent: "bg-blue-500" }
        ];
        const colors = brandColors[index % brandColors.length];

        return (
          <div
            key={service.id}
            className={`comparison-card rounded-xl p-6 text-white relative overflow-hidden ${
              isFirst 
                ? `bg-gradient-to-br ${colors.bg}` 
                : 'bg-white border-2 border-gray-200 text-gray-900'
            }`}
          >
            {data.badge && (
              <div className={`absolute top-4 right-4 px-3 py-1 rounded-full text-xs font-bold ${
                isFirst 
                  ? 'bg-white text-blue-600' 
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {data.badge}
              </div>
            )}
            
            <div className="flex items-center space-x-4 mb-4">
              <div className={`w-16 h-16 rounded-lg flex items-center justify-center ${
                isFirst ? 'bg-white' : 'bg-gray-100'
              }`}>
                <div className={`w-12 h-12 ${colors.accent} rounded-lg flex items-center justify-center`}>
                  <span className="text-white font-bold text-lg">
                    {service.name.charAt(0)}
                  </span>
                </div>
              </div>
              <div>
                <h3 className="text-xl font-bold">{service.name}</h3>
                <p className={isFirst ? 'text-blue-100' : 'text-gray-600'}>
                  Unlimited Plan
                </p>
              </div>
            </div>
            
            <div className="mb-6">
              <div className="flex items-baseline">
                <span className="text-4xl font-bold">
                  {data.pricing?.monthly || '$25'}
                </span>
                <span className="text-xl ml-1">/month</span>
              </div>
              <p className={`text-sm ${isFirst ? 'text-blue-100' : 'text-gray-500'}`}>
                {index === 0 ? '3-month plan, then $30/month' : 'No contract required'}
              </p>
            </div>

            <div className="space-y-3 mb-6">
              {(data.features || ['Unlimited talk, text & data', 'Mobile hotspot included', '5G network access']).map((feature, idx) => (
                <div key={idx} className="flex items-center">
                  <Check className={`mr-3 w-4 h-4 ${isFirst ? 'text-green-300' : 'text-green-500'}`} />
                  <span className="text-sm">{feature}</span>
                </div>
              ))}
            </div>

            <Button 
              className={`w-full py-3 rounded-lg font-semibold transition-colors ${
                isFirst 
                  ? 'bg-white text-blue-600 hover:bg-gray-100' 
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              View Deal →
            </Button>
          </div>
        );
      })}
    </div>
  );
}
