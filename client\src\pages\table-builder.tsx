import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import TemplateSelector from "@/components/table-builder/template-selector";
import FieldConfigurator from "@/components/table-builder/field-configurator";
import LivePreview from "@/components/table-builder/live-preview";
import type { Service } from "@shared/schema";

export default function TableBuilder() {
  const [selectedTemplate, setSelectedTemplate] = useState("affiliate-cards");
  const [selectedServices, setSelectedServices] = useState<number[]>([]);
  const [selectedFields, setSelectedFields] = useState<string[]>([
    "monthlyPrice",
    "dataAllowance",
    "networkCoverage"
  ]);

  const { data: services = [] } = useQuery<Service[]>({
    queryKey: ["/api/services"],
  });

  const handleServiceToggle = (serviceId: number) => {
    setSelectedServices(prev =>
      prev.includes(serviceId)
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  const handleFieldToggle = (field: string) => {
    setSelectedFields(prev =>
      prev.includes(field)
        ? prev.filter(f => f !== field)
        : [...prev, field]
    );
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Table Builder Panel */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Table Builder</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <TemplateSelector
                selectedTemplate={selectedTemplate}
                onTemplateChange={setSelectedTemplate}
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Services
                </label>
                <div className="space-y-2">
                  {services.map((service) => (
                    <label key={service.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedServices.includes(service.id)}
                        onChange={() => handleServiceToggle(service.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-600"
                      />
                      <span className="ml-3 text-sm">{service.name}</span>
                    </label>
                  ))}
                </div>
              </div>

              <FieldConfigurator
                selectedFields={selectedFields}
                onFieldToggle={handleFieldToggle}
                template={selectedTemplate}
              />
            </CardContent>
          </Card>
        </div>

        {/* Live Preview Panel */}
        <div>
          <LivePreview
            services={services.filter(s => selectedServices.includes(s.id))}
            fields={selectedFields}
            template={selectedTemplate}
          />
        </div>
      </div>
    </div>
  );
}
