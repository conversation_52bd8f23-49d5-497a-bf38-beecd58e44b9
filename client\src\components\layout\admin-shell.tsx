import { ReactNode } from "react";
import Sidebar from "./sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Search } from "lucide-react";

interface AdminShellProps {
  children: ReactNode;
}

export default function AdminShell({ children }: AdminShellProps) {
  return (
    <div className="flex min-h-screen bg-gray-100">
      <Sidebar />
      
      <div className="flex-1 flex flex-col">
        {/* Top Admin Bar */}
        <div className="bg-white border-b border-gray-200 px-6 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold text-gray-900">Affiliate Table Builder</h1>
            <span className="px-2 py-1 bg-green-600 text-white text-xs rounded">v2.1.0</span>
          </div>
          <div className="flex items-center space-x-3">
            <Button className="bg-blue-600 hover:bg-blue-700 text-sm">
              <Plus className="w-4 h-4 mr-2" />
              Quick Add Service
            </Button>
            <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6 overflow-auto">
          {children}
        </div>
      </div>
    </div>
  );
}
