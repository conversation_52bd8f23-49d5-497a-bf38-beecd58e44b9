// Table generation utilities
import type { Service } from "@shared/schema";

export interface TableConfiguration {
  template: string;
  services: number[];
  fields: string[];
  style: {
    theme: string;
    showBadges: boolean;
    showCTA: boolean;
    responsiveBreakpoint: string;
  };
}

export interface GeneratedTable {
  html: string;
  css: string;
  javascript?: string;
}

export class TableGenerator {
  static generateHTML(services: Service[], config: TableConfiguration): GeneratedTable {
    const { template, fields, style } = config;

    if (template === "phone-plans-cards") {
      return {
        html: this.generatePhonePlanCards(services, style),
        css: this.generatePhonePlanCardsCSS(style)
      };
    }

    if (template === "affiliate-cards") {
      return {
        html: this.generateAffiliateCards(services, style),
        css: this.generateAffiliateCSS(style)
      };
    }

    // Generate the comparison cards
    const cardsHTML = this.generateComparisonCards(services.slice(0, 2), style);

    // Generate the detailed table
    const tableHTML = this.generateComparisonTable(services, fields, style);

    // Generate CTA section
    const ctaHTML = style.showCTA ? this.generateCTASection(services) : '';

    const html = `
      <div class="affiliate-comparison-table ${style.theme}">
        ${cardsHTML}
        ${tableHTML}
        ${ctaHTML}
      </div>
    `;

    const css = this.generateCSS(style);

    return {
      html: html.trim(),
      css: css.trim()
    };
  }

  private static generateComparisonCards(services: Service[], style: any): string {
    if (services.length === 0) return '';
    
    return `
      <div class="comparison-cards grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        ${services.map((service, index) => this.generateCard(service, index === 0, style)).join('')}
      </div>
    `;
  }

  private static generateCard(service: Service, isPrimary: boolean, style: any): string {
    const data = service.scrapedData as any || {};
    const badge = data.badge || (isPrimary ? 'BEST OVERALL' : 'BEST VALUE');
    
    return `
      <div class="comparison-card ${isPrimary ? 'primary-card' : 'secondary-card'}">
        ${style.showBadges ? `<div class="card-badge">${badge}</div>` : ''}
        <div class="card-header">
          <div class="service-logo">
            ${service.name.charAt(0)}
          </div>
          <div class="service-info">
            <h3>${service.name}</h3>
            <p>Unlimited Plan</p>
          </div>
        </div>
        <div class="pricing">
          <span class="price">${data.pricing?.monthly || '$25'}</span>
          <span class="period">/month</span>
        </div>
        <div class="features">
          ${(data.features || ['Unlimited data', 'Mobile hotspot', '5G access']).map(feature => 
            `<div class="feature">✓ ${feature}</div>`
          ).join('')}
        </div>
        <button class="cta-button">View Deal →</button>
      </div>
    `;
  }

  private static generateComparisonTable(services: Service[], fields: string[], style: any): string {
    if (services.length === 0) return '';
    
    const fieldLabels = {
      monthlyPrice: 'Monthly Price',
      dataAllowance: 'Data Allowance',
      mobileHotspot: 'Mobile Hotspot',
      network: 'Network',
      access5G: '5G Access'
    };
    
    return `
      <div class="comparison-table-container">
        <h4>Detailed Comparison</h4>
        <table class="comparison-table">
          <thead>
            <tr>
              <th>Features</th>
              ${services.map(service => `<th>${service.name}</th>`).join('')}
            </tr>
          </thead>
          <tbody>
            ${fields.map(field => `
              <tr>
                <td>${fieldLabels[field as keyof typeof fieldLabels] || field}</td>
                ${services.map(service => `<td>${this.getFieldValue(service, field)}</td>`).join('')}
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  private static generateCTASection(services: Service[]): string {
    return `
      <div class="cta-section">
        <h5>Ready to Switch?</h5>
        <p>Compare all features and find the perfect plan for your needs.</p>
        <div class="cta-buttons">
          ${services.map(service => 
            `<button class="cta-btn cta-${service.name.toLowerCase().replace(/\s+/g, '-')}">
              Get ${service.name} Deal
            </button>`
          ).join('')}
        </div>
      </div>
    `;
  }

  private static getFieldValue(service: Service, field: string): string {
    const data = service.scrapedData as any || {};
    
    switch (field) {
      case 'monthlyPrice':
        return data.pricing?.monthly || '$25';
      case 'dataAllowance':
        return 'Unlimited';
      case 'mobileHotspot':
        return 'Unlimited';
      case 'network':
        return data.network || 'Verizon';
      case 'access5G':
        return '✓';
      default:
        return '—';
    }
  }

  private static generatePhonePlanCards(services: Service[], style: any): string {
    return `
      <div class="phone-plan-cards-grid">
        ${services.map((service, index) => this.generatePhonePlanCard(service, index, style)).join('')}
      </div>
    `;
  }

  private static generatePhonePlanCard(service: Service, index: number, style: any): string {
    const data = service.scrapedData as any || {};

    return `
      <div class="phone-plan-card">
        <!-- Badge -->
        ${data.badge ? `
          <div class="plan-badge" style="background-color: ${data.badgeColor || '#10B981'}">
            ${data.badge}
          </div>
        ` : ''}

        <!-- Logo and Carrier -->
        <div class="carrier-header">
          <div class="carrier-logo" style="background-color: ${data.logoBackground || '#000'}; color: ${data.logoColor || '#fff'}">
            ${data.logo || service.name.charAt(0)}
          </div>
          <div class="carrier-info">
            <h3 class="carrier-name">${data.carrier || service.name}</h3>
            <p class="plan-name">${data.planName || service.name + ' Plan'}</p>
          </div>
        </div>

        <!-- Price -->
        <div class="price-section">
          <div class="price-display">
            <span class="price-amount">${data.price || '$30.00'}</span>
            <span class="price-unit">${data.priceUnit || '/month'}</span>
          </div>
          ${data.dataInfo ? `<p class="data-info">${data.dataInfo}</p>` : ''}
        </div>

        <!-- Features -->
        ${data.features && Array.isArray(data.features) ? `
          <div class="features-list">
            ${data.features.map(feature => `
              <div class="feature-item">
                <svg class="feature-check" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                ${feature}
              </div>
            `).join('')}
          </div>
        ` : ''}

        <!-- Buttons -->
        <div class="card-actions">
          <button class="view-plans-btn">
            ${data.viewPlansButton || 'View Plans'}
          </button>
          <button class="compare-btn">
            ${data.compareButton || 'Compare Details'}
          </button>
        </div>
      </div>
    `;
  }

  private static generateAffiliateCards(services: Service[], style: any): string {
    return `
      <div class="affiliate-plans-grid">
        ${services.map((service, index) => this.generateAffiliatePlanCard(service, index, style)).join('')}
      </div>
    `;
  }

  private static generateAffiliatePlanCard(service: Service, index: number, style: any): string {
    const data = service.scrapedData as any || {};
    const isPopular = index === 0 || index === 1;
    const badge = isPopular ? 'Most Popular' : '';
    
    // Extract pricing and features from service data
    const monthlyPrice = data.pricing?.monthly || '$25';
    const dataAllowance = data.features?.includes('Unlimited') ? 'Unlimited' : '30GB';
    const network = data.network || 'Verizon';
    const features = data.features || [
      'Unlimited minutes',
      'Unlimited texts', 
      '10GB hotspot',
      '480p video streaming',
      'Int\'l texting included'
    ];

    return `
      <div class="plan-card ${isPopular ? 'popular-plan' : ''}">
        ${badge ? `<div class="plan-badge">${badge}</div>` : ''}
        
        <div class="plan-header">
          <h3 class="plan-name">${service.name}</h3>
          <div class="plan-lines">1 Line</div>
          <div class="plan-network">${network}</div>
        </div>

        <div class="plan-pricing">
          <span class="price-symbol">$</span>
          <span class="price-amount">${monthlyPrice.replace('$', '')}</span>
          <span class="price-period">/mo.</span>
          <div class="price-note">tax included</div>
        </div>

        <div class="plan-data">
          <span class="data-amount">${dataAllowance}</span>
          <span class="data-type">high-speed</span>
        </div>

        <div class="plan-features">
          ${features.slice(0, 8).map(feature => `
            <div class="feature-item">${feature}</div>
          `).join('')}
        </div>

        <div class="plan-actions">
          <a href="#" class="details-link">See details</a>
          <a href="#" class="cta-button">Get Started</a>
        </div>

        <div class="compare-section">
          <button class="add-compare-btn">Add to Compare</button>
        </div>
      </div>
    `;
  }

  private static generatePhonePlanCardsCSS(style: any): string {
    return `
      .phone-plan-cards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 1.5rem;
        max-width: 1200px;
        margin: 0 auto;
        padding: 1.5rem;
      }

      .phone-plan-card {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 1.5rem;
        position: relative;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .phone-plan-card:hover {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }

      .plan-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        color: white;
        font-size: 0.875rem;
        font-weight: 600;
        margin-bottom: 1rem;
      }

      .carrier-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
      }

      .carrier-logo {
        width: 3rem;
        height: 3rem;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1.125rem;
        margin-right: 0.75rem;
      }

      .carrier-info {
        flex: 1;
      }

      .carrier-name {
        font-size: 1.125rem;
        font-weight: 600;
        color: #111827;
        margin: 0 0 0.25rem 0;
      }

      .plan-name {
        font-size: 0.875rem;
        color: #6b7280;
        margin: 0;
      }

      .price-section {
        margin-bottom: 1.5rem;
      }

      .price-display {
        display: flex;
        align-items: baseline;
        margin-bottom: 0.5rem;
      }

      .price-amount {
        font-size: 2rem;
        font-weight: 700;
        color: #111827;
      }

      .price-unit {
        font-size: 1rem;
        color: #6b7280;
        margin-left: 0.25rem;
      }

      .data-info {
        font-size: 0.875rem;
        color: #6b7280;
        margin: 0;
      }

      .features-list {
        margin-bottom: 1.5rem;
      }

      .feature-item {
        display: flex;
        align-items: center;
        padding: 0.5rem 0;
        font-size: 0.875rem;
        color: #374151;
      }

      .feature-check {
        width: 1rem;
        height: 1rem;
        color: #10b981;
        margin-right: 0.5rem;
        flex-shrink: 0;
      }

      .card-actions {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
      }

      .view-plans-btn {
        width: 100%;
        background: #2563eb;
        color: white;
        border: none;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s ease;
      }

      .view-plans-btn:hover {
        background: #1d4ed8;
      }

      .compare-btn {
        width: 100%;
        background: white;
        color: #374151;
        border: 1px solid #d1d5db;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .compare-btn:hover {
        background: #f9fafb;
        border-color: #9ca3af;
      }

      @media (max-width: 768px) {
        .phone-plan-cards-grid {
          grid-template-columns: 1fr;
          padding: 1rem;
        }

        .phone-plan-card {
          padding: 1rem;
        }

        .price-amount {
          font-size: 1.75rem;
        }
      }
    `;
  }

  private static generateAffiliateCSS(style: any): string {
    return `
      .affiliate-plans-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        max-width: 1200px;
        margin: 0 auto;
        padding: 1rem;
      }

      .plan-card {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 1.5rem;
        position: relative;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .plan-card:hover {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }

      .popular-plan {
        border-color: #2563eb;
        border-width: 2px;
      }

      .plan-badge {
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%);
        background: #f59e0b;
        color: white;
        padding: 0.25rem 1rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
      }

      .plan-header {
        text-align: center;
        margin-bottom: 1.5rem;
      }

      .plan-name {
        font-size: 1.25rem;
        font-weight: 700;
        color: #111827;
        margin-bottom: 0.5rem;
      }

      .plan-lines {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 0.25rem;
      }

      .plan-network {
        font-size: 0.875rem;
        color: #6b7280;
      }

      .plan-pricing {
        text-align: center;
        margin-bottom: 1.5rem;
        padding: 1rem 0;
        border-bottom: 1px solid #f3f4f6;
      }

      .price-symbol {
        font-size: 1.5rem;
        font-weight: 600;
        color: #111827;
        vertical-align: top;
      }

      .price-amount {
        font-size: 3rem;
        font-weight: 700;
        color: #111827;
        line-height: 1;
      }

      .price-period {
        font-size: 1.25rem;
        color: #6b7280;
        vertical-align: bottom;
      }

      .price-note {
        font-size: 0.75rem;
        color: #6b7280;
        margin-top: 0.25rem;
      }

      .plan-data {
        text-align: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #f3f4f6;
      }

      .data-amount {
        font-size: 1.25rem;
        font-weight: 600;
        color: #111827;
        display: block;
      }

      .data-type {
        font-size: 0.875rem;
        color: #6b7280;
      }

      .plan-features {
        margin-bottom: 1.5rem;
      }

      .feature-item {
        padding: 0.5rem 0;
        font-size: 0.875rem;
        color: #374151;
        border-bottom: 1px solid #f3f4f6;
      }

      .feature-item:last-child {
        border-bottom: none;
      }

      .plan-actions {
        margin-bottom: 1rem;
      }

      .details-link {
        display: block;
        text-align: center;
        color: #2563eb;
        text-decoration: none;
        font-size: 0.875rem;
        margin-bottom: 0.75rem;
      }

      .details-link:hover {
        text-decoration: underline;
      }

      .cta-button {
        display: block;
        width: 100%;
        background: #2563eb;
        color: white;
        text-decoration: none;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        font-weight: 600;
        text-align: center;
        transition: background-color 0.2s ease;
      }

      .cta-button:hover {
        background: #1d4ed8;
      }

      .compare-section {
        text-align: center;
        padding-top: 1rem;
        border-top: 1px solid #f3f4f6;
      }

      .add-compare-btn {
        background: transparent;
        border: 1px solid #d1d5db;
        color: #6b7280;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .add-compare-btn:hover {
        border-color: #2563eb;
        color: #2563eb;
      }

      @media (max-width: 768px) {
        .affiliate-plans-grid {
          grid-template-columns: 1fr;
          padding: 0.5rem;
        }
        
        .plan-card {
          padding: 1rem;
        }
        
        .price-amount {
          font-size: 2.5rem;
        }
      }
    `;
  }

  private static generateCSS(style: any): string {
    return `
      .affiliate-comparison-table {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
      }
      
      .comparison-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
      }
      
      .comparison-card {
        border-radius: 12px;
        padding: 2rem;
        position: relative;
        transition: transform 0.3s ease;
      }
      
      .comparison-card:hover {
        transform: translateY(-4px);
      }
      
      .primary-card {
        background: linear-gradient(135deg, #2563eb, #1d4ed8);
        color: white;
      }
      
      .secondary-card {
        background: white;
        border: 2px solid #e5e7eb;
        color: #111827;
      }
      
      .card-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: bold;
      }
      
      .primary-card .card-badge {
        background: white;
        color: #2563eb;
      }
      
      .secondary-card .card-badge {
        background: #f3f4f6;
        color: #6b7280;
      }
      
      .comparison-table-container {
        background: white;
        border-radius: 12px;
        border: 1px solid #e5e7eb;
        overflow: hidden;
        margin-bottom: 2rem;
      }
      
      .comparison-table {
        width: 100%;
        border-collapse: collapse;
      }
      
      .comparison-table th,
      .comparison-table td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
      }
      
      .comparison-table th {
        background: #f9fafb;
        font-weight: 600;
        color: #6b7280;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
      }
      
      .cta-section {
        text-align: center;
        background: #f9fafb;
        border-radius: 8px;
        padding: 2rem;
      }
      
      .cta-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        justify-content: center;
        margin-top: 1rem;
      }
      
      .cta-btn {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        color: white;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
      }
      
      @media (max-width: 768px) {
        .comparison-cards {
          grid-template-columns: 1fr;
        }
        
        .comparison-table-container {
          overflow-x: auto;
        }
        
        .cta-buttons {
          flex-direction: column;
        }
      }
    `;
  }
}
