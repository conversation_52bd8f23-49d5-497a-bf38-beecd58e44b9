import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Shield,
  Smartphone,
  Server,
  Mail,
  ArrowRight,
  Plus,
  Heart,
  DollarSign,
  Home,
  Car,
  Plane,
  Book,
  ShoppingCart,
  Briefcase
} from "lucide-react";
import type { Service, Niche, PlatformSetting } from "@shared/schema";
import { Link } from "wouter";

const nicheIcons = {
  shield: Shield,
  smartphone: Smartphone,
  server: Server,
  mail: Mail,
  heart: Heart,
  "dollar-sign": DollarSign,
  home: Home,
  car: Car,
  plane: Plane,
  book: Book,
  "shopping-cart": ShoppingCart,
  briefcase: Briefcase,
};

export default function Dashboard() {
  const { data: niches = [], isLoading: nichesLoading } = useQuery<Niche[]>({
    queryKey: ["/api/niches"],
  });

  const { data: services = [], isLoading: servicesLoading } = useQuery<Service[]>({
    queryKey: ["/api/services"],
  });

  const { data: platformSettings = [] } = useQuery<PlatformSetting[]>({
    queryKey: ["/api/platform-settings"],
  });

  const platformName = platformSettings.find(s => s.key === "platform_name")?.value || "Universal Affiliate Platform";

  const getServicesForNiche = (niche: Niche) => {
    // Generic matching - services belong to a niche if their category matches the niche name
    return services.filter(s => s.category === niche.name);
  };

  if (nichesLoading || servicesLoading) {
    return (
      <div className="p-8">
        <h1 className="text-3xl font-bold mb-8">Dashboard</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">{platformName}</h1>
        <p className="text-gray-600 mt-2">Manage your niches, services, and create professional comparison tables</p>
      </div>

      {niches.length === 0 && !nichesLoading && (
        <Card className="mb-8 border-dashed border-2 border-gray-300">
          <CardContent className="text-center py-12">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <Plus className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Get Started</h3>
            <p className="text-gray-600 mb-4">
              Create your first niche to start building comparison tables for your affiliate marketing platform.
            </p>
            <Link href="/niche-setup">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Niche
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {niches.map((niche) => {
          const IconComponent = nicheIcons[niche.icon as keyof typeof nicheIcons] || Server;
          const nicheServices = getServicesForNiche(niche);
          
          return (
            <Card key={niche.id} className="hover:shadow-lg transition-shadow cursor-pointer group">
              <Link href={`/niche/${niche.slug}`}>
                <CardHeader className="text-center pb-4">
                  <div className="mx-auto p-4 bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mb-4 group-hover:bg-blue-200 transition-colors">
                    <IconComponent className="h-8 w-8 text-blue-600" />
                  </div>
                  <CardTitle className="text-xl font-semibold">{niche.name}</CardTitle>
                  <CardDescription className="text-sm">{niche.description}</CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="space-y-2 mb-4">
                    <div className="text-2xl font-bold text-gray-900">{nicheServices.length}</div>
                    <p className="text-sm text-gray-500">Services Available</p>
                  </div>
                  
                  <div className="space-y-2">
                    {nicheServices.slice(0, 3).map((service) => (
                      <div key={service.id} className="text-xs text-gray-600 bg-gray-50 px-2 py-1 rounded">
                        {service.name}
                      </div>
                    ))}
                    {nicheServices.length > 3 && (
                      <div className="text-xs text-gray-500">
                        +{nicheServices.length - 3} more services
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-center mt-4 text-blue-600 group-hover:text-blue-800">
                    <span className="text-sm font-medium mr-2">Explore Niche</span>
                    <ArrowRight className="h-4 w-4" />
                  </div>
                </CardContent>
              </Link>
            </Card>
          );
        })}
      </div>

      <div className="mt-12 grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks to get started</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Link href="/services">
              <Button variant="outline" className="w-full justify-start">
                <Server className="h-4 w-4 mr-2" />
                Add New Service
              </Button>
            </Link>
            <Link href="/table-builder">
              <Button variant="outline" className="w-full justify-start">
                <Shield className="h-4 w-4 mr-2" />
                Create Comparison Table
              </Button>
            </Link>
            <Link href="/gutenberg">
              <Button variant="outline" className="w-full justify-start">
                <Mail className="h-4 w-4 mr-2" />
                WordPress Integration
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest services added across all niches</CardDescription>
          </CardHeader>
          <CardContent>
            {services.length === 0 ? (
              <p className="text-gray-500 text-sm">No services added yet</p>
            ) : (
              <div className="space-y-3">
                {services.slice(-5).map((service) => {
                  const niche = niches.find(n => service.category === n.name);
                  
                  return (
                    <div key={service.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <h3 className="font-medium text-gray-900">{service.name}</h3>
                        <p className="text-sm text-gray-500">{niche?.name || service.category}</p>
                      </div>
                      {niche && (
                        <Link href={`/niche/${niche.slug}/service/${service.id}`}>
                          <Button size="sm" variant="ghost">
                            View
                          </Button>
                        </Link>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}