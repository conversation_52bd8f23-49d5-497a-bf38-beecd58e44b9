import React from 'react';
import { Service } from '../../types';

interface PhonePlanCardsProps {
  services: Service[];
  configuration?: {
    layout?: string;
    showBadges?: boolean;
    showLogos?: boolean;
    showFeatures?: boolean;
    showButtons?: boolean;
    cardStyle?: string;
  };
}

export function PhonePlanCards({ services, configuration = {} }: PhonePlanCardsProps) {
  const {
    showBadges = true,
    showLogos = true,
    showFeatures = true,
    showButtons = true
  } = configuration;

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto p-6">
      {services.map((service) => {
        const data = service.scrapedData || {};
        
        return (
          <div key={service.id} className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow">
            {/* Badge */}
            {showBadges && data.badge && (
              <div className="mb-4">
                <span 
                  className="inline-block px-3 py-1 rounded-full text-sm font-medium text-white"
                  style={{ backgroundColor: data.badgeColor || '#10B981' }}
                >
                  {data.badge}
                </span>
              </div>
            )}

            {/* Logo and Carrier */}
            <div className="flex items-center mb-4">
              {showLogos && data.logo && (
                <div 
                  className="w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold text-lg mr-3"
                  style={{ backgroundColor: data.logoBackground || '#000' }}
                >
                  {data.logo}
                </div>
              )}
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{data.carrier || service.name}</h3>
                <p className="text-sm text-gray-600">{data.planName}</p>
              </div>
            </div>

            {/* Price */}
            <div className="mb-4">
              <div className="flex items-baseline">
                <span className="text-3xl font-bold text-gray-900">{data.price || '$0.00'}</span>
                <span className="text-gray-600 ml-1">{data.priceUnit || '/month'}</span>
              </div>
              {data.dataInfo && (
                <p className="text-sm text-gray-600 mt-1">{data.dataInfo}</p>
              )}
            </div>

            {/* Features */}
            {showFeatures && data.features && Array.isArray(data.features) && (
              <div className="mb-6">
                <ul className="space-y-2">
                  {data.features.map((feature: string, index: number) => (
                    <li key={index} className="flex items-center text-sm text-gray-700">
                      <svg 
                        className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" 
                        fill="currentColor" 
                        viewBox="0 0 20 20"
                      >
                        <path 
                          fillRule="evenodd" 
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
                          clipRule="evenodd" 
                        />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Buttons */}
            {showButtons && (
              <div className="space-y-3">
                <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                  {data.viewPlansButton || 'View Plans'}
                </button>
                <button className="w-full bg-white text-gray-700 py-2 px-4 rounded-lg font-medium border border-gray-300 hover:bg-gray-50 transition-colors">
                  {data.compareButton || 'Compare Details'}
                </button>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
