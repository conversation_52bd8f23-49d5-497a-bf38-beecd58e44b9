import { Link, useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { 
  BarChart3, 
  Settings, 
  Table, 
  FileText, 
  Home,
  Plus,
  Shield,
  Smartphone,
  Server,
  Mail,
  ChevronDown,
  ChevronRight
} from "lucide-react";
import { useState } from "react";
import type { Niche, Table as TableType, Service } from "@shared/schema";

const navigation = [
  { name: 'Dashboard', href: '/', icon: Home },
  { name: 'Create Niche', href: '/niche-setup', icon: Plus },
  { name: 'Add Services', href: '/services', icon: Server },
  { name: 'Table Builder', href: '/table-builder', icon: Table },
  { name: 'Affiliate Tables', href: '/affiliate-tables', icon: BarChart3 },
  { name: 'Gutenberg Integration', href: '/gutenberg', icon: FileText },
  { name: 'Platform Settings', href: '/settings', icon: Settings },
];

const nicheIcons = {
  shield: Shield,
  smartphone: Smartphone,
  server: Server,
  mail: Mail,
};

export default function Sidebar() {
  const [location] = useLocation();
  const [expandedNiches, setExpandedNiches] = useState<number[]>([]);

  const { data: niches = [], isLoading: nichesLoading } = useQuery<Niche[]>({
    queryKey: ["/api/niches"],
  });

  const { data: allTables = [], isLoading: tablesLoading } = useQuery<TableType[]>({
    queryKey: ["/api/tables"],
  });

  const { data: allServices = [] } = useQuery<Service[]>({
    queryKey: ["/api/services"],
  });

  const toggleNiche = (nicheId: number) => {
    setExpandedNiches(prev => 
      prev.includes(nicheId) 
        ? prev.filter(id => id !== nicheId)
        : [...prev, nicheId]
    );
  };

  const getTablesForNiche = (nicheId: number) => {
    return allTables.filter(table => table.nicheId === nicheId);
  };

  const getServicesForNiche = (niche: Niche) => {
    return allServices.filter(s => s.category === niche.name);
  };

  return (
    <div className="bg-gray-800 text-white w-64 min-h-screen flex flex-col">
      <div className="p-6 border-b border-gray-700">
        <h1 className="text-xl font-bold">Affiliate Table Builder</h1>
      </div>
      
      <nav className="flex-1 px-4 py-6 overflow-y-auto">
        <div className="space-y-8">
          <div>
            <h2 className="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-3">
              Main Navigation
            </h2>
            <ul className="space-y-1">
              {navigation.map((item) => {
                const isActive = location === item.href;
                return (
                  <li key={item.name}>
                    <Link href={item.href}>
                      <span className={`flex items-center px-4 py-2 text-sm hover:bg-gray-700 cursor-pointer ${
                        isActive ? 'bg-gray-700 text-white' : 'text-gray-300'
                      }`}>
                        <item.icon className="w-4 h-4 mr-3" />
                        {item.name}
                      </span>
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>

          <div>
            <h2 className="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-3">
              Niches & Tables
            </h2>
            {nichesLoading ? (
              <div className="text-gray-400 text-sm">Loading niches...</div>
            ) : (
              <div className="space-y-1">
                {niches.map((niche) => {
                  const isExpanded = expandedNiches.includes(niche.id);
                  const tables = getTablesForNiche(niche.id);
                  const IconComponent = nicheIcons[niche.icon as keyof typeof nicheIcons] || Table;
                  
                  return (
                    <div key={niche.id}>
                      <button
                        onClick={() => toggleNiche(niche.id)}
                        className="w-full flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 cursor-pointer"
                      >
                        <IconComponent className="w-4 h-4 mr-3" />
                        <span className="flex-1 text-left">{niche.name}</span>
                        {isExpanded ? (
                          <ChevronDown className="w-3 h-3" />
                        ) : (
                          <ChevronRight className="w-3 h-3" />
                        )}
                      </button>
                      
                      {isExpanded && (
                        <div className="ml-4 border-l border-gray-600 pl-4 py-1">
                          <Link href={`/niche/${niche.slug}`}>
                            <span className="block px-3 py-1 text-xs text-gray-400 hover:text-white hover:bg-gray-700 cursor-pointer rounded mb-2 font-medium">
                              📊 Niche Dashboard
                            </span>
                          </Link>
                          <Link href={`/niche/${niche.slug}/compare`}>
                            <span className="block px-3 py-1 text-xs text-gray-400 hover:text-white hover:bg-gray-700 cursor-pointer rounded mb-2">
                              ⚖️ Compare Services
                            </span>
                          </Link>
                          <div className="text-xs text-gray-500 py-1 mb-1">Services:</div>
                          {getServicesForNiche(niche).map((service) => (
                            <Link key={service.id} href={`/niche/${niche.slug}/service/${service.id}`}>
                              <span className="block px-3 py-1 text-xs text-gray-400 hover:text-white hover:bg-gray-700 cursor-pointer rounded">
                                {service.name}
                              </span>
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </nav>
    </div>
  );
}