interface FieldConfiguratorProps {
  selectedFields: string[];
  onFieldToggle: (field: string) => void;
  template: string;
}

const fieldsByTemplate = {
  "phone-plans": [
    { id: "monthlyPrice", label: "Monthly Price" },
    { id: "dataAllowance", label: "Data Allowance" },
    { id: "networkCoverage", label: "Network Coverage" },
    { id: "mobileHotspot", label: "Mobile Hotspot" },
    { id: "internationalFeatures", label: "International Features" },
    { id: "access5G", label: "5G Access" },
  ],
  "vpn-services": [
    { id: "monthlyPrice", label: "Monthly Price" },
    { id: "serverCount", label: "Server Count" },
    { id: "countries", label: "Countries" },
    { id: "protocols", label: "Protocols" },
    { id: "logging", label: "No-Logs Policy" },
    { id: "streaming", label: "Streaming Support" },
  ],
  "web-hosting": [
    { id: "monthlyPrice", label: "Monthly Price" },
    { id: "storage", label: "Storage" },
    { id: "bandwidth", label: "Bandwidth" },
    { id: "domains", label: "Domains" },
    { id: "support", label: "Support" },
    { id: "uptime", label: "Uptime Guarantee" },
  ],
  "email-marketing": [
    { id: "monthlyPrice", label: "Monthly Price" },
    { id: "subscribers", label: "Subscribers" },
    { id: "emails", label: "Emails per Month" },
    { id: "automation", label: "Automation" },
    { id: "templates", label: "Templates" },
    { id: "analytics", label: "Analytics" },
  ],
};

export default function FieldConfigurator({ selectedFields, onFieldToggle, template }: FieldConfiguratorProps) {
  const fields = fieldsByTemplate[template as keyof typeof fieldsByTemplate] || fieldsByTemplate["phone-plans"];

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">Display Fields</label>
      <div className="space-y-2">
        {fields.map((field) => (
          <label key={field.id} className="flex items-center">
            <input
              type="checkbox"
              checked={selectedFields.includes(field.id)}
              onChange={() => onFieldToggle(field.id)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-600"
            />
            <span className="ml-3 text-sm">{field.label}</span>
          </label>
        ))}
      </div>
    </div>
  );
}
