import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Plus, Settings, Save, ArrowRight, ArrowLeft } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import type { FieldTemplate, InsertNiche } from "@shared/schema";

const iconOptions = [
  { value: "server", label: "Server" },
  { value: "smartphone", label: "Smartphone" },
  { value: "shield", label: "Shield" },
  { value: "mail", label: "Mail" },
  { value: "heart", label: "Heart" },
  { value: "dollar-sign", label: "Dollar Sign" },
  { value: "home", label: "Home" },
  { value: "car", label: "Car" },
  { value: "plane", label: "Travel" },
  { value: "book", label: "Education" },
  { value: "shopping-cart", label: "Shopping" },
  { value: "briefcase", label: "Business" }
];

export default function NicheSetup() {
  const [step, setStep] = useState(1);
  const [nicheData, setNicheData] = useState<InsertNiche>({
    name: "",
    slug: "",
    description: "",
    icon: ""
  });
  const [selectedFields, setSelectedFields] = useState<number[]>([]);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: fieldTemplates = [] } = useQuery<FieldTemplate[]>({
    queryKey: ["/api/field-templates"],
  });

  const createNicheMutation = useMutation({
    mutationFn: async (data: InsertNiche) => {
      const response = await fetch("/api/niches", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error("Failed to create niche");
      return response.json();
    },
    onSuccess: (niche) => {
      queryClient.invalidateQueries({ queryKey: ["/api/niches"] });
      configureFieldsMutation.mutate({ nicheId: niche.id, fieldIds: selectedFields });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create niche. Please try again.",
        variant: "destructive",
      });
    },
  });

  const configureFieldsMutation = useMutation({
    mutationFn: async ({ nicheId, fieldIds }: { nicheId: number; fieldIds: number[] }) => {
      const promises = fieldIds.map((fieldId, index) =>
        fetch(`/api/niches/${nicheId}/configuration`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            fieldTemplateId: fieldId,
            displayOrder: index,
            isVisible: true,
          }),
        })
      );
      await Promise.all(promises);
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Niche created and configured successfully!",
      });
      // Reset form
      setNicheData({ name: "", slug: "", description: "", icon: "" });
      setSelectedFields([]);
      setStep(1);
    },
    onError: () => {
      toast({
        title: "Warning",
        description: "Niche created but field configuration failed. You can configure fields later.",
        variant: "destructive",
      });
    },
  });

  const handleNicheDataChange = (field: keyof InsertNiche, value: string) => {
    setNicheData(prev => ({
      ...prev,
      [field]: value,
      ...(field === "name" && { slug: value.toLowerCase().replace(/[^a-z0-9]+/g, "-").replace(/^-|-$/g, "") })
    }));
  };

  const handleFieldToggle = (fieldId: number) => {
    setSelectedFields(prev =>
      prev.includes(fieldId)
        ? prev.filter(id => id !== fieldId)
        : [...prev, fieldId]
    );
  };

  const handleSubmit = () => {
    if (!nicheData.name || !nicheData.slug) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }
    createNicheMutation.mutate(nicheData);
  };

  const renderStep1 = () => (
    <Card>
      <CardHeader>
        <CardTitle>Step 1: Basic Information</CardTitle>
        <CardDescription>
          Set up the basic details for your new niche
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="name">Niche Name *</Label>
          <Input
            id="name"
            value={nicheData.name}
            onChange={(e) => handleNicheDataChange("name", e.target.value)}
            placeholder="e.g., VPN Services, Web Hosting, Health Supplements"
          />
        </div>
        
        <div>
          <Label htmlFor="slug">URL Slug *</Label>
          <Input
            id="slug"
            value={nicheData.slug}
            onChange={(e) => handleNicheDataChange("slug", e.target.value)}
            placeholder="e.g., vpn-services, web-hosting, health-supplements"
          />
        </div>
        
        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={nicheData.description}
            onChange={(e) => handleNicheDataChange("description", e.target.value)}
            placeholder="Brief description of this niche and what types of services it includes"
            rows={3}
          />
        </div>
        
        <div>
          <Label htmlFor="icon">Icon</Label>
          <Select value={nicheData.icon} onValueChange={(value) => handleNicheDataChange("icon", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Choose an icon for this niche" />
            </SelectTrigger>
            <SelectContent>
              {iconOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex justify-end">
          <Button 
            onClick={() => setStep(2)}
            disabled={!nicheData.name || !nicheData.slug}
          >
            Next: Configure Fields <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  const renderStep2 = () => (
    <Card>
      <CardHeader>
        <CardTitle>Step 2: Field Configuration</CardTitle>
        <CardDescription>
          Select which fields you want to track for services in this niche
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {fieldTemplates.map((template) => (
            <div
              key={template.id}
              className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                selectedFields.includes(template.id)
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200 hover:border-gray-300"
              }`}
              onClick={() => handleFieldToggle(template.id)}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">{template.label}</h3>
                  <p className="text-sm text-gray-500 capitalize">{template.type}</p>
                  {template.isRequired && (
                    <Badge variant="secondary" className="mt-1">Required</Badge>
                  )}
                </div>
                <Checkbox
                  checked={selectedFields.includes(template.id)}
                  onChange={() => handleFieldToggle(template.id)}
                />
              </div>
            </div>
          ))}
        </div>
        
        <div className="flex justify-between">
          <Button variant="outline" onClick={() => setStep(1)}>
            <ArrowLeft className="mr-2 h-4 w-4" /> Back
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={selectedFields.length === 0 || createNicheMutation.isPending}
          >
            {createNicheMutation.isPending ? "Creating..." : "Create Niche"}
            <Save className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Create New Niche</h1>
        <p className="text-gray-600 mt-2">
          Set up a new niche for your affiliate comparison platform
        </p>
      </div>

      <div className="mb-6">
        <div className="flex items-center space-x-4">
          <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
            step >= 1 ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-600"
          }`}>
            1
          </div>
          <div className={`h-1 w-16 ${step >= 2 ? "bg-blue-600" : "bg-gray-200"}`} />
          <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
            step >= 2 ? "bg-blue-600 text-white" : "bg-gray-200 text-gray-600"
          }`}>
            2
          </div>
        </div>
        <div className="flex justify-between mt-2 text-sm text-gray-600">
          <span>Basic Info</span>
          <span>Configure Fields</span>
        </div>
      </div>

      {step === 1 ? renderStep1() : renderStep2()}
    </div>
  );
}
