import { useQuery } from "@tanstack/react-query";
import { useRoute } from "wouter";
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowLeft, BarChart3 } from "lucide-react";
import type { Service, Niche } from "@shared/schema";
import { Link } from "wouter";

export default function NicheCompare() {
  const [match, params] = useRoute("/niche/:slug/compare");
  const nicheSlug = params?.slug;
  const [selectedServices, setSelectedServices] = useState<number[]>([]);

  const { data: niches = [] } = useQuery<Niche[]>({
    queryKey: ["/api/niches"],
  });

  const { data: services = [] } = useQuery<Service[]>({
    queryKey: ["/api/services"],
  });

  const currentNiche = niches.find(n => n.slug === nicheSlug);
  const nicheServices = services.filter(s => s.category === currentNiche?.name || 
    (currentNiche?.name === "VPN Services" && s.category === "VPN") ||
    (currentNiche?.name === "Phone Plans" && s.category === "Phone Plans") ||
    (currentNiche?.name === "Web Hosting" && s.category === "Web Hosting") ||
    (currentNiche?.name === "Email Marketing" && s.category === "Email Marketing"));

  const toggleServiceSelection = (serviceId: number) => {
    setSelectedServices(prev => 
      prev.includes(serviceId) 
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  const selectedServiceData = nicheServices.filter(s => selectedServices.includes(s.id));

  if (!currentNiche) {
    return (
      <div className="p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Niche Not Found</h1>
        <p className="text-gray-600">The requested niche could not be found.</p>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8">
        <Link href={`/niche/${nicheSlug}`}>
          <Button variant="outline" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to {currentNiche.name}
          </Button>
        </Link>
        
        <div className="flex items-center mb-4">
          <BarChart3 className="h-8 w-8 text-blue-600 mr-4" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Compare {currentNiche.name}</h1>
            <p className="text-gray-600 mt-1">Select services to compare side by side</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Service Selection */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Select Services</CardTitle>
            <CardDescription>Choose up to 4 services to compare</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {nicheServices.map((service) => (
                <div key={service.id} className="flex items-center space-x-3">
                  <Checkbox
                    id={`service-${service.id}`}
                    checked={selectedServices.includes(service.id)}
                    onCheckedChange={() => toggleServiceSelection(service.id)}
                    disabled={selectedServices.length >= 4 && !selectedServices.includes(service.id)}
                  />
                  <label
                    htmlFor={`service-${service.id}`}
                    className="flex-1 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                  >
                    <div>
                      <div>{service.name}</div>
                      <div className="text-xs text-gray-500">
                        {(service.scrapedData as any)?.pricing?.monthly || "Price not available"}
                      </div>
                    </div>
                  </label>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Comparison Table */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Service Comparison</CardTitle>
            <CardDescription>
              {selectedServices.length === 0 ? "Select services to see comparison" : 
               `Comparing ${selectedServices.length} service${selectedServices.length > 1 ? 's' : ''}`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {selectedServices.length === 0 ? (
              <div className="text-center py-12">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Select services from the left to start comparing</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Feature</th>
                      {selectedServiceData.map((service) => (
                        <th key={service.id} className="text-center py-3 px-4 font-medium text-gray-900 min-w-[150px]">
                          <div>
                            <div>{service.name}</div>
                            <Badge variant="secondary" className="text-xs mt-1">
                              {service.category}
                            </Badge>
                          </div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    <tr>
                      <td className="py-3 px-4 font-medium text-gray-900">Monthly Price</td>
                      {selectedServiceData.map((service) => (
                        <td key={service.id} className="py-3 px-4 text-center">
                          <div className="text-lg font-semibold text-green-600">
                            {(service.scrapedData as any)?.pricing?.monthly || "N/A"}
                          </div>
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="py-3 px-4 font-medium text-gray-900">Network/Coverage</td>
                      {selectedServiceData.map((service) => (
                        <td key={service.id} className="py-3 px-4 text-center text-gray-600">
                          {(service.scrapedData as any)?.network || "Available"}
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="py-3 px-4 font-medium text-gray-900">Key Features</td>
                      {selectedServiceData.map((service) => (
                        <td key={service.id} className="py-3 px-4">
                          <div className="space-y-1">
                            {((service.scrapedData as any)?.features || []).slice(0, 3).map((feature: string, index: number) => (
                              <Badge key={index} variant="outline" className="text-xs block">
                                {feature}
                              </Badge>
                            ))}
                          </div>
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="py-3 px-4 font-medium text-gray-900">Website</td>
                      {selectedServiceData.map((service) => (
                        <td key={service.id} className="py-3 px-4 text-center">
                          <a 
                            href={service.website} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 text-sm"
                          >
                            Visit Site
                          </a>
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="py-3 px-4 font-medium text-gray-900">Action</td>
                      {selectedServiceData.map((service) => (
                        <td key={service.id} className="py-3 px-4 text-center">
                          <Link href={`/niche/${nicheSlug}/service/${service.id}`}>
                            <Button size="sm" variant="outline">
                              View Details
                            </Button>
                          </Link>
                        </td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}