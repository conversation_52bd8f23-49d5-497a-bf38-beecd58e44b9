import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import Dashboard from "@/pages/dashboard";
import ServiceManagement from "@/pages/service-management";
import TableBuilder from "@/pages/table-builder";
import GutenbergIntegration from "@/pages/gutenberg-integration";
import AffiliateTableDemo from "@/pages/affiliate-table-demo";
import NicheDashboard from "@/pages/niche-dashboard";
import ServiceDetail from "@/pages/service-detail";
import NicheCompare from "@/pages/niche-compare";
import NicheSetup from "@/pages/niche-setup";
import PlatformSettings from "@/pages/platform-settings";
import AdminShell from "@/components/layout/admin-shell";
import NotFound from "@/pages/not-found";

function Router() {
  return (
    <AdminShell>
      <Switch>
        <Route path="/" component={Dashboard} />
        <Route path="/services" component={ServiceManagement} />
        <Route path="/table-builder" component={TableBuilder} />
        <Route path="/affiliate-tables" component={AffiliateTableDemo} />
        <Route path="/gutenberg" component={GutenbergIntegration} />
        <Route path="/niche-setup" component={NicheSetup} />
        <Route path="/settings" component={PlatformSettings} />
        <Route path="/niche/:slug" component={NicheDashboard} />
        <Route path="/niche/:slug/compare" component={NicheCompare} />
        <Route path="/niche/:nicheSlug/service/:serviceId" component={ServiceDetail} />
        <Route component={NotFound} />
      </Switch>
    </AdminShell>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
