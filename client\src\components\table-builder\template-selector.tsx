import { Button } from "@/components/ui/button";

interface TemplateSelectorProps {
  selectedTemplate: string;
  onTemplateChange: (template: string) => void;
}

const templates = [
  {
    id: "affiliate-cards",
    name: "Affiliate Cards",
    description: "Professional plan comparison cards",
  },
  {
    id: "phone-plans",
    name: "Phone Plans Table",
    description: "Price, Data, Features",
  },
  {
    id: "vpn-services",
    name: "VPN Comparison",
    description: "Speed, Security, Price",
  },
  {
    id: "web-hosting",
    name: "Web Hosting",
    description: "Storage, Bandwidth, Support",
  },
];

export default function TemplateSelector({ selectedTemplate, onTemplateChange }: TemplateSelectorProps) {
  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">Template</label>
      <div className="grid grid-cols-2 gap-3">
        {templates.map((template) => (
          <Button
            key={template.id}
            variant={selectedTemplate === template.id ? "default" : "outline"}
            className="p-3 h-auto justify-start"
            onClick={() => onTemplateChange(template.id)}
          >
            <div className="text-left">
              <div className="font-medium text-sm">{template.name}</div>
              <div className="text-xs opacity-70">{template.description}</div>
            </div>
          </Button>
        ))}
      </div>
    </div>
  );
}
