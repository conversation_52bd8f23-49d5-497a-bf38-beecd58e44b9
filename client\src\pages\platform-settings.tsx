import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Settings, Save, RefreshCw } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import FieldConfigurator from "@/components/configuration/field-configurator";
import type { PlatformSetting } from "@shared/schema";

export default function PlatformSettings() {
  const [settings, setSettings] = useState<Record<string, any>>({});
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: platformSettings = [], isLoading } = useQuery<PlatformSetting[]>({
    queryKey: ["/api/platform-settings"],
    onSuccess: (data) => {
      const settingsMap = data.reduce((acc, setting) => {
        acc[setting.key] = setting.value;
        return acc;
      }, {} as Record<string, any>);
      setSettings(settingsMap);
    },
  });

  const updateSettingMutation = useMutation({
    mutationFn: async ({ key, value }: { key: string; value: any }) => {
      const response = await fetch(`/api/platform-settings/${key}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ value }),
      });
      if (!response.ok) throw new Error("Failed to update setting");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/platform-settings"] });
      toast({
        title: "Success",
        description: "Settings updated successfully!",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update settings.",
        variant: "destructive",
      });
    },
  });

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleSaveSettings = () => {
    Object.entries(settings).forEach(([key, value]) => {
      updateSettingMutation.mutate({ key, value });
    });
  };

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 space-y-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Platform Settings</h1>
        <p className="text-gray-600 mt-2">
          Configure your affiliate platform settings and field templates
        </p>
      </div>

      {/* General Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            General Settings
          </CardTitle>
          <CardDescription>
            Basic platform configuration and branding
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="platform_name">Platform Name</Label>
            <Input
              id="platform_name"
              value={settings.platform_name || ""}
              onChange={(e) => handleSettingChange("platform_name", e.target.value)}
              placeholder="Universal Affiliate Platform"
            />
          </div>

          <div>
            <Label htmlFor="default_currency">Default Currency</Label>
            <Input
              id="default_currency"
              value={settings.default_currency || ""}
              onChange={(e) => handleSettingChange("default_currency", e.target.value)}
              placeholder="USD"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="gutenberg_enabled"
              checked={settings.gutenberg_enabled || false}
              onCheckedChange={(checked) => handleSettingChange("gutenberg_enabled", checked)}
            />
            <Label htmlFor="gutenberg_enabled">Enable WordPress Gutenberg Integration</Label>
          </div>

          <div className="flex justify-end">
            <Button 
              onClick={handleSaveSettings}
              disabled={updateSettingMutation.isPending}
            >
              <Save className="w-4 h-4 mr-2" />
              {updateSettingMutation.isPending ? "Saving..." : "Save Settings"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Field Templates Configuration */}
      <FieldConfigurator />

      {/* Platform Information */}
      <Card>
        <CardHeader>
          <CardTitle>Platform Information</CardTitle>
          <CardDescription>
            About this niche-agnostic affiliate platform
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="prose prose-sm max-w-none">
            <h4>Features</h4>
            <ul>
              <li><strong>Niche-Agnostic:</strong> Create comparison tables for any industry or niche</li>
              <li><strong>WordPress Integration:</strong> Generate Gutenberg blocks for seamless WordPress integration</li>
              <li><strong>Flexible Field System:</strong> Define custom fields for different types of services</li>
              <li><strong>Professional Templates:</strong> Multiple table layouts and styling options</li>
              <li><strong>Service Management:</strong> Add and manage services across different niches</li>
              <li><strong>Responsive Design:</strong> Tables work perfectly on all devices</li>
            </ul>

            <h4>Getting Started</h4>
            <ol>
              <li>Create your first niche using the "Create Niche" option</li>
              <li>Configure which fields you want to track for that niche</li>
              <li>Add services to your niche</li>
              <li>Build comparison tables using the Table Builder</li>
              <li>Export to WordPress using Gutenberg integration</li>
            </ol>

            <h4>WordPress Gutenberg Integration</h4>
            <p>
              This platform maintains full compatibility with WordPress Gutenberg blocks, 
              allowing you to easily embed professional comparison tables into your WordPress posts and pages.
              The generated blocks are responsive and SEO-friendly.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
